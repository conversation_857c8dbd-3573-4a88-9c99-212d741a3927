<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Whatsapp{
    
    public function send($number,$message){
        try {
            require 'vendor/autoload.php';

            $token = 'd3C5H4RudHj6ZdRO1gGqNxRLmbmnvHaGVy02badpxA9l3+TED6NIiqEcLBePSz83zoWWeZwzUNGnw62d1kj4Nw==';

            $client = new \GuzzleHttp\Client(['base_uri' => 'https://app.lenna.ai/app/public/api/']);

            $response = $client->request(
                'POST', 
                'PdR6qd/send-hsm',
                [
                    'json' => [
                        "integrationId" => 174,
                        "token" => $token,
                        "channel" => "whatsapp",
                        "type" => "broadcast",
                        "category" => "hsm",
                        "templateName" => "info_nama_pasien",
                        "messageTitle" => "title",
                        "phone" => [$number],
                        "templateParams" => $message
                    ]
                ]
            );

            $data = $response->getBody();
            $data = json_decode($data, true);
            return $data;
        } catch (\Throwable $th) {
            return [
                'status' => 'error',
                'http_code' => 500,
                'message' => 'service unavailable'
            ];
        }
    }


    public function kirim($number,$message){ 
        require 'vendor/autoload.php';

		$token = 'd3C5H4RudHj6ZdRO1gGqNxRLmbmnvHaGVy02badpxA9l3+TED6NIiqEcLBePSz83zoWWeZwzUNGnw62d1kj4Nw==';
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://app.lenna.ai/app/public/api/']);

        $response = $client->request(
            'POST', 
            'PdR6qd/send-hsm',
            [
                'json' => [
                    "integrationId" => 174,
                    "token" => $token,
                    "channel" => "whatsapp",
                    "type" => "broadcast",
                    "category" => "hsm",
                    "templateName" => "format_setelah_melakukan_reservasi",
                    "messageTitle" => "title",
                    "phone" => [$number],
                    "templateParams" => $message
                ]
            ]
        );
        
        return $response->getBody();
    }

    public function kirim2($number,$message){ 
        require 'vendor/autoload.php';

		$token = 'd3C5H4RudHj6ZdRO1gGqNxRLmbmnvHaGVy02badpxA9l3+TED6NIiqEcLBePSz83zoWWeZwzUNGnw62d1kj4Nw==';
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://app.lenna.ai/app/public/api/']);

        $response = $client->request(
            'POST', 
            'PdR6qd/send-hsm',
            [
                'json' => [
                    "integrationId" => 174,
                    "token" => $token,
                    "channel" => "whatsapp",
                    "type" => "broadcast",
                    "category" => "hsm",
                    "templateName" => "format_dapat_kamar_new",
                    "messageTitle" => "title",
                    "phone" => [$number],
                    "templateParams" => $message
                ]
            ]
        );
        
        return $response->getBody();
    }
    public function kirim_terima_towerc($number,$message){ 
        require 'vendor/autoload.php';

		$token = 'd3C5H4RudHj6ZdRO1gGqNxRLmbmnvHaGVy02badpxA9l3+TED6NIiqEcLBePSz83zoWWeZwzUNGnw62d1kj4Nw==';
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://app.lenna.ai/app/public/api/']);

        $response = $client->request(
            'POST', 
            'PdR6qd/send-hsm',
            [
                'json' => [
                    "integrationId" => 174,
                    "token" => $token,
                    "channel" => "whatsapp",
                    "type" => "broadcast",
                    "category" => "hsm",
                    "templateName" => "terima_reservasi_c_new",
                    "messageTitle" => "title",
                    "phone" => [$number],
                    "templateParams" => $message
                ]
            ]
        );
        
        return $response->getBody();
    }

    public function kirim_tunggu_towerc($number,$message){ 
        require 'vendor/autoload.php';

		$token = 'd3C5H4RudHj6ZdRO1gGqNxRLmbmnvHaGVy02badpxA9l3+TED6NIiqEcLBePSz83zoWWeZwzUNGnw62d1kj4Nw==';
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://app.lenna.ai/app/public/api/']);

        $response = $client->request(
            'POST', 
            'PdR6qd/send-hsm',
            [
                'json' => [
                    "integrationId" => 174,
                    "token" => $token,
                    "channel" => "whatsapp",
                    "type" => "broadcast",
                    "category" => "hsm",
                    "templateName" => "tunggukmr_reservasi_c",
                    "messageTitle" => "title",
                    "phone" => [$number],
                    "templateParams" => $message
                ]
            ]
        );
        
        return $response->getBody();
    }


}
