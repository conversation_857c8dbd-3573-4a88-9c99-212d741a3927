<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <!-- <meta name="robots" content="noindex,nofollow" /> -->
    <title>Dashboard IGD v2</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/plugins/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="assets/plugins/perfectscroll/perfect-scrollbar.css" rel="stylesheet">
    <link href="assets/plugins/DataTables/datatables.min.css" rel="stylesheet">   

    
    <!-- Theme Styles -->
    <link href="assets/css/main.css" rel="stylesheet">
    <link href="assets/css/custom.css" rel="stylesheet">
</head>

<style>
    .hijau {
        background-color: #10ac84;
        color: white;
    }

    .kuning{
        background: #feb019d9;
        color: white;
    }

    .merah {
        background-color: #c9001dd9;
        color: white;
    }

    .orange {
        background-color: #ff6d00;
        color: white;
    }
</style>

<body>
<div class="page-container">
    <div class="page-header">
        <nav class="navbar navbar-expand-lg d-flex justify-content-between">
            <div class="row" style="width:100%">
                <div class="col-md-4 col-sm-12 d-flex justify-content-start"><h3 class="mt-3">DASHBOARD IGD </h3></div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-center"><div id="tanggalku" style="font-size: 40px;"></div></div>
                <div class="col-md-4 col-sm-12 d-flex justify-content-end pull-right">
                    <div class="row">
                        <div class="col-12">
                            <h4 style="text-align: end;"><?= date("l, d F Y") ?></h4>
                        </div>
                        <div class="col-12">
                            <h3 style="text-align: end;" class="total_pasien"><b>Total Pasien ()</b></h3>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="" id="navbarNav">
                <h3 class="mt-3">DASHBOARD IGD</h3> 
                <div id="tanggalku" style="font-size: 40px; margin-left:400px"></div> 
            </div>
            <div class="logo">
                <h4><?= date("l, d F Y") ?></h4>
                <h3><b>Total Pasien (<?= $total['total'] ?>)</b></h3>     
            </div> -->
            <!-- <div class="" id="headerNav">
                
            </div> -->
        </nav>
    </div>
    <div class="page-content" style="margin-top:0px">
        <div class="main-wrapper">
            <div class="row">
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                            <table id="myTable" class="display" style="width:100%">
                                <thead>
                                    <tr style="text-align:center">
                                        <th>NORM</th>
                                        <th>Nama Pasien</th>
                                        <th>Diagnosa Medis Utama</th>
                                        <th>Diagnosa Medis Sekunder</th>
                                        <th>Permintaan Rawat</th>
                                        <th>Jawaban Admission</th>
                                        <th>Asal Pasien</th>
                                        <th>Tgl.Masuk</th>
                                        <th>Lab PK</th>
                                        <th>Lama Perawatan</th>
                                        <th>Level</th>
                                        <th>DPJP</th>
                                        <th style="width:100px">Billing</th>
                                        <th>Tgl.Pengkajian</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <script src="assets/plugins/jquery/jquery-3.4.1.min.js"></script>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="assets/plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="assets/plugins/perfectscroll/perfect-scrollbar.min.js"></script>
    <script src="assets/plugins/DataTables/datatables.min.js"></script>
    <script src="assets/js/main.min.js"></script>
    <script src="assets/js/pages/datatables.js"></script>


    <script>
    $(document).ready( function () {
        var table = $('#myTable').DataTable({
            responsive: true,
            columnDefs: [ { type: 'date', 'targets': [6] } ],
            order: [[ 6, 'asc' ]], 
            "columnDefs": [
                            {
                                "targets": [ 10 ],
                                "visible": false,
                                "searchable": false
                            }
                            // {
                            //     "targets": [ 12 ],
                            //     "render": $.fn.dataTable.render.number( '.', ',', 0, 'Rp ' )
                            // }
                        ],
            "createdRow": function (row, data, dataIndex) {
                if(data[10] == "merah" ) {
                    $(row).addClass("merah");
                } else if (data[10] == "orange") {
                    $(row).addClass("orange");
                } else if (data[10] == "kuning") {
                    $(row).addClass("kuning");
                } else if (data[10] == "hijau") {
                    $(row).addClass("hijau");
                }
            },
            ajax: {
                    url: 'Pulang/get_data_pasien',
                    type: 'GET',
                    complete: function (data) {
                        var res=data['responseJSON'];
                        // $(".tanggal").text(res['time'])
                        $(".total_pasien").text('Total Pasien ('+res['recordsTotal']+')')
                    },
                },
            ordering: false,
            searching: false,
            lengthChange: false
        });

        // setTimeout(function() {
        // table.ajax.reload();
        // }, 1000);

        function reloadDatatable () {
            $('#myTable').DataTable().ajax.reload(null, true);
        }; 
        setInterval( reloadDatatable , 5000 );

        // setInterval(function(){ 
        //     var info = table.page.info();
        //     var pageNum = (info.page < info.pages) ? info.page + 1 : 0;
        //     table.page(pageNum).draw(false);    
        // }, 15000); 
    });
    </script>

<script type="text/javascript">
    window.setTimeout("waktu()",1000);
    function waktu() {
      var tanggal = new Date();
      setTimeout("waktu()",1000);
      document.getElementById("tanggalku").innerHTML
      = tanggal.getHours()+" : "+tanggal.getMinutes()+" : "+tanggal.getSeconds();
    }
  </script>
</body>
</html>
