<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Stockart extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('Stockart_model');
        $this->load->helper('url');
        $this->load->library('session');
    }

    public function index()
    {
        // Check if user is logged in
        if ($this->session->userdata('status_login') != 'oke') {
            // Set session data to indicate redirect to stockart
            $this->session->set_userdata('redirect_to', 'stockart');
            redirect('');
            return;
        }

        $data['page_content'] = 'stockart';
        $data['title'] = 'Stockart';
        $this->load->view('template_dashboard', $data);
    }

    public function debug()
    {
        // Test database connection
        if ($this->Stockart_model->test_database_connection()) {
            echo "Database Connection: OK<br>";
        } else {
            echo "Database Connection: FAILED<br>";
        }

        // Test model
        $result = $this->Stockart_model->get_stockart_data();
        echo "Data Count: " . count($result) . "<br>";

        if (count($result) > 0) {
            echo "Sample Data:<br>";
            echo "<pre>";
            print_r(array_slice($result, 0, 3));
            echo "</pre>";
        } else {
            echo "No data found<br>";
        }
    }

    public function get_data()
    {
        try {
            $data = $this->Stockart_model->get_stockart_data();

            // Format data untuk DataTables dengan tombol action
            $result = array();
            foreach ($data as $row) {
                $action_btn = '<button class="btn btn-sm btn-info" onclick="viewDetail(\'' . $row['id_pengajuan'] . '\')" title="Lihat Detail">
                                <i class="fas fa-eye"></i> Detail
                               </button>';
                
                // Get status text
                $status_text = $this->Stockart_model->get_status_text($row['status'] ?? 0);
                
                // Add status badge styling
                $status_badge = '';
                switch($row['status']) {
                    case 1:
                        $status_badge = '<span class="badge bg-warning">' . $status_text . '</span>';
                        break;
                    case 2:
                        $status_badge = '<span class="badge bg-success">' . $status_text . '</span>';
                        break;
                    case 3:
                        $status_badge = '<span class="badge bg-danger">' . $status_text . '</span>';
                        break;
                    default:
                        $status_badge = '<span class="badge bg-secondary">' . $status_text . '</span>';
                }

                // Format tanggal dibuat
                $dibuat_pada = '-';
                if (!empty($row['dibuat_pada']) && $row['dibuat_pada'] != '0000-00-00 00:00:00') {
                    try {
                        $dibuat_pada = date('d-m-Y H:i:s', strtotime($row['dibuat_pada']));
                    } catch (Exception $e) {
                        $dibuat_pada = $row['dibuat_pada']; // fallback to original value
                    }
                }

                $result[] = array(
                    $row['nama_pasien'] ?? '',
                    $row['norm_pasien'] ?? '',
                    $row['nokun'] ?? '',
                    $row['nama_dokter_tujuan'] ?? '',
                    $dibuat_pada,
                    $status_badge,
                    $action_btn,
                    $row['status'] ?? 0  // Add raw status value for sorting
                );
            }

            $output = array(
                "draw" => intval($this->input->post('draw')),
                "recordsTotal" => count($result),
                "recordsFiltered" => count($result),
                "data" => $result
            );

            echo json_encode($output);
        } catch (Exception $e) {
            echo json_encode(array(
                "draw" => 0,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            ));
        }
    }

    public function get_detail()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');

        if (!$id_pengajuan) {
            echo json_encode(array('success' => false, 'message' => 'ID Pengajuan tidak ditemukan'));
            return;
        }

        try {
            $data = $this->Stockart_model->get_stockart_detail($id_pengajuan);
            $pengajuan_info = $this->Stockart_model->get_pengajuan_info($id_pengajuan);
            echo json_encode(array('success' => true, 'data' => $data, 'pengajuan_info' => $pengajuan_info));
        } catch (Exception $e) {
            echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        }
    }

    public function insertOrder()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');

        if (!$id_pengajuan) {
            echo json_encode(array('success' => false, 'message' => 'ID Pengajuan tidak ditemukan'));
            return;
        }

        $user_id = $this->session->userdata('id');
        $result = $this->Stockart_model->process_order($id_pengajuan, $user_id);

        echo json_encode($result);
    }

    public function export_excel()
    {
        try {
            // Get data stockart
            $stockart_data = $this->Stockart_model->get_stockart_data();

            // Generate filename
            $filename = 'Data_Stockart_' . date('Y-m-d_H-i-s') . '.xlsx';

            // Set headers for download
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // Create Excel content using HTML table format that Excel can read
            echo '<?xml version="1.0"?>';
            echo '<?mso-application progid="Excel.Sheet"?>';
            echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"';
            echo ' xmlns:o="urn:schemas-microsoft-com:office:office"';
            echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"';
            echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"';
            echo ' xmlns:html="http://www.w3.org/TR/REC-html40">';

            // Main sheet - Data Stockart
            echo '<Worksheet ss:Name="Data Stockart">';
            echo '<Table>';

            // Headers
            echo '<Row>';
            echo '<Cell><Data ss:Type="String">No</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Nama Pasien</Data></Cell>';
            echo '<Cell><Data ss:Type="String">NORM</Data></Cell>';
            echo '<Cell><Data ss:Type="String">No. Kunjungan</Data></Cell>';
            echo '<Cell><Data ss:Type="String">DPJP</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Dibuat Pada</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Status</Data></Cell>';
            echo '</Row>';

            // Fill data
            $no = 1;
            foreach ($stockart_data as $data) {
                // Format status
                $status_text = $this->Stockart_model->get_status_text($data['status']);

                // Format tanggal
                $dibuat_pada = '-';
                if (!empty($data['dibuat_pada']) && $data['dibuat_pada'] != '0000-00-00 00:00:00') {
                    $dibuat_pada = date('d-m-Y H:i:s', strtotime($data['dibuat_pada']));
                }

                echo '<Row>';
                echo '<Cell><Data ss:Type="Number">' . $no . '</Data></Cell>';
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($data['nama_pasien']) ? $data['nama_pasien'] : '') . '</Data></Cell>';
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($data['norm_pasien']) ? $data['norm_pasien'] : '') . '</Data></Cell>';
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($data['nokun']) ? $data['nokun'] : '') . '</Data></Cell>';
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($data['nama_dokter_tujuan']) ? $data['nama_dokter_tujuan'] : '') . '</Data></Cell>';
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars($dibuat_pada) . '</Data></Cell>';
                echo '<Cell><Data ss:Type="String">' . htmlspecialchars($status_text) . '</Data></Cell>';
                echo '</Row>';

                $no++;
            }

            echo '</Table>';
            echo '</Worksheet>';

            // Create detail sheet
            echo '<Worksheet ss:Name="Detail Items">';
            echo '<Table>';

            // Headers for detail data
            echo '<Row>';
            echo '<Cell><Data ss:Type="String">No</Data></Cell>';
            echo '<Cell><Data ss:Type="String">ID Pengajuan</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Nama Pasien</Data></Cell>';
            echo '<Cell><Data ss:Type="String">NORM</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Nama Barang</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Quantity</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Note</Data></Cell>';
            echo '<Cell><Data ss:Type="String">Status</Data></Cell>';
            echo '</Row>';

            // Fill detail data
            $detail_no = 1;
            foreach ($stockart_data as $data) {
                $details = $this->Stockart_model->get_stockart_detail($data['id_pengajuan']);
                $status_text = $this->Stockart_model->get_status_text($data['status']);

                foreach ($details as $detail) {
                    echo '<Row>';
                    echo '<Cell><Data ss:Type="Number">' . $detail_no . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="String">' . htmlspecialchars($data['id_pengajuan']) . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($data['nama_pasien']) ? $data['nama_pasien'] : '') . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($data['norm_pasien']) ? $data['norm_pasien'] : '') . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($detail['nama_barang']) ? $detail['nama_barang'] : '') . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="Number">' . htmlspecialchars(isset($detail['quantity']) ? $detail['quantity'] : '') . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="String">' . htmlspecialchars(isset($detail['note']) ? $detail['note'] : '') . '</Data></Cell>';
                    echo '<Cell><Data ss:Type="String">' . htmlspecialchars($status_text) . '</Data></Cell>';
                    echo '</Row>';

                    $detail_no++;
                }
            }

            echo '</Table>';
            echo '</Worksheet>';
            echo '</Workbook>';

        } catch (Exception $e) {
            log_message('error', 'Export Excel Error: ' . $e->getMessage());
            echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        }
    }
}
