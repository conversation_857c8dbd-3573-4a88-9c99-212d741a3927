<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Stockart extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('Stockart_model');
        $this->load->helper('url');
        $this->load->library('session');
    }

    public function index()
    {
        // Check if user is logged in
        if ($this->session->userdata('status_login') != 'oke') {
            // Set session data to indicate redirect to stockart
            $this->session->set_userdata('redirect_to', 'stockart');
            redirect('');
            return;
        }

        $data['page_content'] = 'stockart';
        $data['title'] = 'Stockart';
        $this->load->view('template_dashboard', $data);
    }

    public function debug()
    {
        // Test database connection
        if ($this->Stockart_model->test_database_connection()) {
            echo "Database Connection: OK<br>";
        } else {
            echo "Database Connection: FAILED<br>";
        }

        // Test model
        $result = $this->Stockart_model->get_stockart_data();
        echo "Data Count: " . count($result) . "<br>";

        if (count($result) > 0) {
            echo "Sample Data:<br>";
            echo "<pre>";
            print_r(array_slice($result, 0, 3));
            echo "</pre>";
        } else {
            echo "No data found<br>";
        }
    }

    public function get_data()
    {
        try {
            $data = $this->Stockart_model->get_stockart_data();

            // Format data untuk DataTables dengan tombol action
            $result = array();
            foreach ($data as $row) {
                $action_btn = '<button class="btn btn-sm btn-info" onclick="viewDetail(\'' . $row['id_pengajuan'] . '\')" title="Lihat Detail">
                                <i class="fas fa-eye"></i> Detail
                               </button>';
                
                // Get status text
                $status_text = $this->Stockart_model->get_status_text($row['status'] ?? 0);
                
                // Add status badge styling
                $status_badge = '';
                switch($row['status']) {
                    case 1:
                        $status_badge = '<span class="badge bg-warning">' . $status_text . '</span>';
                        break;
                    case 2:
                        $status_badge = '<span class="badge bg-success">' . $status_text . '</span>';
                        break;
                    case 3:
                        $status_badge = '<span class="badge bg-danger">' . $status_text . '</span>';
                        break;
                    default:
                        $status_badge = '<span class="badge bg-secondary">' . $status_text . '</span>';
                }

                // Format tanggal dibuat
                $dibuat_pada = '-';
                if (!empty($row['dibuat_pada']) && $row['dibuat_pada'] != '0000-00-00 00:00:00') {
                    try {
                        $dibuat_pada = date('d-m-Y H:i:s', strtotime($row['dibuat_pada']));
                    } catch (Exception $e) {
                        $dibuat_pada = $row['dibuat_pada']; // fallback to original value
                    }
                }

                $result[] = array(
                    $row['nama_pasien'] ?? '',
                    $row['norm_pasien'] ?? '',
                    $row['nokun'] ?? '',
                    $row['nama_dokter_tujuan'] ?? '',
                    $dibuat_pada,
                    $status_badge,
                    $action_btn,
                    $row['status'] ?? 0  // Add raw status value for sorting
                );
            }

            $output = array(
                "draw" => intval($this->input->post('draw')),
                "recordsTotal" => count($result),
                "recordsFiltered" => count($result),
                "data" => $result
            );

            echo json_encode($output);
        } catch (Exception $e) {
            echo json_encode(array(
                "draw" => 0,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            ));
        }
    }

    public function get_detail()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');

        if (!$id_pengajuan) {
            echo json_encode(array('success' => false, 'message' => 'ID Pengajuan tidak ditemukan'));
            return;
        }

        try {
            $data = $this->Stockart_model->get_stockart_detail($id_pengajuan);
            $pengajuan_info = $this->Stockart_model->get_pengajuan_info($id_pengajuan);
            echo json_encode(array('success' => true, 'data' => $data, 'pengajuan_info' => $pengajuan_info));
        } catch (Exception $e) {
            echo json_encode(array('success' => false, 'message' => $e->getMessage()));
        }
    }

    public function insertOrder()
    {
        $id_pengajuan = $this->input->post('id_pengajuan');

        if (!$id_pengajuan) {
            echo json_encode(array('success' => false, 'message' => 'ID Pengajuan tidak ditemukan'));
            return;
        }

        $user_id = $this->session->userdata('id');
        $result = $this->Stockart_model->process_order($id_pengajuan, $user_id);

        echo json_encode($result);
    }

    public function export_excel()
    {
        try {
            // Load the SimpleExcelWriter library
            $this->load->library('SimpleExcelWriter');

            // Get data stockart
            $stockart_data = $this->Stockart_model->get_stockart_data();

            // Create main sheet
            $this->simpleexcelwriter->createSheet('Data Stockart')
                ->setHeaderStyle('background-color: #4472C4; color: white; font-weight: bold;')
                ->addHeaders(array('No', 'Nama Pasien', 'NORM', 'No. Kunjungan', 'DPJP', 'Dibuat Pada', 'Status'));

            // Add main data
            $no = 1;
            foreach ($stockart_data as $data) {
                // Format status
                $status_text = $this->Stockart_model->get_status_text($data['status']);

                // Format tanggal
                $dibuat_pada = '-';
                if (!empty($data['dibuat_pada']) && $data['dibuat_pada'] != '0000-00-00 00:00:00') {
                    $dibuat_pada = date('d-m-Y H:i:s', strtotime($data['dibuat_pada']));
                }

                $this->simpleexcelwriter->addRow(array(
                    $no,
                    isset($data['nama_pasien']) ? $data['nama_pasien'] : '',
                    isset($data['norm_pasien']) ? $data['norm_pasien'] : '',
                    isset($data['nokun']) ? $data['nokun'] : '',
                    isset($data['nama_dokter_tujuan']) ? $data['nama_dokter_tujuan'] : '',
                    $dibuat_pada,
                    $status_text
                ));

                $no++;
            }

            // Create detail sheet
            $this->simpleexcelwriter->createSheet('Detail Items')
                ->setHeaderStyle('background-color: #70AD47; color: white; font-weight: bold;')
                ->addHeaders(array('No', 'ID Pengajuan', 'Nama Pasien', 'NORM', 'Nama Barang', 'Quantity', 'Note', 'Status'));

            // Add detail data
            $detail_no = 1;
            foreach ($stockart_data as $data) {
                $details = $this->Stockart_model->get_stockart_detail($data['id_pengajuan']);
                $status_text = $this->Stockart_model->get_status_text($data['status']);

                foreach ($details as $detail) {
                    $this->simpleexcelwriter->addRow(array(
                        $detail_no,
                        $data['id_pengajuan'],
                        isset($data['nama_pasien']) ? $data['nama_pasien'] : '',
                        isset($data['norm_pasien']) ? $data['norm_pasien'] : '',
                        isset($detail['nama_barang']) ? $detail['nama_barang'] : '',
                        isset($detail['quantity']) ? $detail['quantity'] : '',
                        isset($detail['note']) ? $detail['note'] : '',
                        $status_text
                    ));

                    $detail_no++;
                }
            }

            // Generate filename and output
            $filename = 'Data_Stockart_' . date('Y-m-d_H-i-s') . '.xls';
            $this->simpleexcelwriter->output($filename);

        } catch (Exception $e) {
            log_message('error', 'Export Excel Error: ' . $e->getMessage());

            // Fallback to CSV if Excel creation fails
            $this->export_csv_fallback();
        }
    }



    private function export_csv_fallback()
    {
        try {
            // Get data stockart
            $stockart_data = $this->Stockart_model->get_stockart_data();

            // Generate filename
            $filename = 'Data_Stockart_' . date('Y-m-d_H-i-s') . '.csv';

            // Set headers for download
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // Create file pointer connected to the output stream
            $output = fopen('php://output', 'w');

            // Add BOM to fix UTF-8 in Excel
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

            // Add main data section header
            fputcsv($output, array('=== DATA STOCKART ==='));
            fputcsv($output, array(''));

            // Main data headers
            fputcsv($output, array('No', 'Nama Pasien', 'NORM', 'No. Kunjungan', 'DPJP', 'Dibuat Pada', 'Status'));

            // Main data
            $no = 1;
            foreach ($stockart_data as $data) {
                // Format status
                $status_text = $this->Stockart_model->get_status_text($data['status']);

                // Format tanggal
                $dibuat_pada = '-';
                if (!empty($data['dibuat_pada']) && $data['dibuat_pada'] != '0000-00-00 00:00:00') {
                    $dibuat_pada = date('d-m-Y H:i:s', strtotime($data['dibuat_pada']));
                }

                fputcsv($output, array(
                    $no,
                    isset($data['nama_pasien']) ? $data['nama_pasien'] : '',
                    isset($data['norm_pasien']) ? $data['norm_pasien'] : '',
                    isset($data['nokun']) ? $data['nokun'] : '',
                    isset($data['nama_dokter_tujuan']) ? $data['nama_dokter_tujuan'] : '',
                    $dibuat_pada,
                    $status_text
                ));

                $no++;
            }

            // Add separator
            fputcsv($output, array(''));
            fputcsv($output, array(''));
            fputcsv($output, array('=== DETAIL ITEMS ==='));
            fputcsv($output, array(''));

            // Detail data headers
            fputcsv($output, array('No', 'ID Pengajuan', 'Nama Pasien', 'NORM', 'Nama Barang', 'Quantity', 'Note', 'Status'));

            // Detail data
            $detail_no = 1;
            foreach ($stockart_data as $data) {
                $details = $this->Stockart_model->get_stockart_detail($data['id_pengajuan']);
                $status_text = $this->Stockart_model->get_status_text($data['status']);

                foreach ($details as $detail) {
                    fputcsv($output, array(
                        $detail_no,
                        $data['id_pengajuan'],
                        isset($data['nama_pasien']) ? $data['nama_pasien'] : '',
                        isset($data['norm_pasien']) ? $data['norm_pasien'] : '',
                        isset($detail['nama_barang']) ? $detail['nama_barang'] : '',
                        isset($detail['quantity']) ? $detail['quantity'] : '',
                        isset($detail['note']) ? $detail['note'] : '',
                        $status_text
                    ));

                    $detail_no++;
                }
            }

            fclose($output);

        } catch (Exception $e) {
            log_message('error', 'CSV Fallback Error: ' . $e->getMessage());
            echo json_encode(array('success' => false, 'message' => 'Export failed: ' . $e->getMessage()));
        }
    }


}
