/*------------------------------------------------------------
[Dashboard Styles]
Styling khusus untuk dashboard Tower Monitoring System
------------------------------------------------------------*/

/*------------------------------------------------------------
[1. NAVBAR STYLES]
------------------------------------------------------------*/

.navbar {
    min-height: 70px;
    /* Navbar lebih besar */
    padding: 0.8rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    /* Lebih besar dari 1.2rem */
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.02);
    color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-brand i {
    font-size: 1.6rem;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

.navbar-nav {
    align-items: center;
}

.navbar-nav .nav-link {
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    margin: 0 3px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.navbar-nav .nav-link:hover::before {
    left: 100%;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.25);
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.navbar-nav .nav-link i {
    margin-right: 0.4rem;
    font-size: 0.9rem;
}

.user-info {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 8px 18px;
    margin-left: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.logout-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    border-radius: 25px;
    padding: 8px 18px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    margin-left: 10px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    color: white;
}

/*------------------------------------------------------------
[2. MAIN CONTENT LAYOUT]
------------------------------------------------------------*/

.main-content {
    min-height: calc(100vh - 70px);
    /* Sesuaikan dengan tinggi navbar baru */
    padding: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Fix untuk dashboard kosong - hilangkan scrollbar */
html {
    height: 100%;
    overflow-x: hidden;
}

body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}



.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/*------------------------------------------------------------
[3. DASHBOARD WELCOME SECTION]
------------------------------------------------------------*/

.dashboard-welcome {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 3rem 0;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
}

.dashboard-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.dashboard-welcome .container-fluid {
    position: relative;
    z-index: 1;
}

.dashboard-welcome h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-welcome p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/*------------------------------------------------------------
[4. EXPORT BUTTONS STYLING]
------------------------------------------------------------*/

.dt-buttons {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.dt-button {
    border-radius: 6px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    border: none !important;
    transition: all 0.3s ease !important;
    text-transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.dt-button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.dt-button.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
}

.dt-button.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a) !important;
}

.dt-button.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
    color: white !important;
}

.dt-button.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34, #155724) !important;
}

/* Custom Export Buttons */
.export-buttons {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-export {
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 16px;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-export:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-export i {
    font-size: 12px;
}

/*------------------------------------------------------------
[5. CARD AND FORM STYLING]
------------------------------------------------------------*/

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: #ffffff;
    color: #333333;
    border-radius: 12px 12px 0 0 !important;
    padding: 18px 24px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-body {
    padding: 24px;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    border-radius: 8px;
    padding: 10px 24px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 8px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-1px);
}

.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 16px 12px;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.001);
}

.table tbody td {
    padding: 14px 12px;
    vertical-align: middle;
}

.alert {
    border: none;
    border-radius: 10px;
    padding: 16px 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

/*------------------------------------------------------------
[6. STATUS COLOR CLASSES]
------------------------------------------------------------*/

/* Status warna untuk monitoring PA */
.hijau {
    background-color: #10ac84 !important;
    color: white !important;
}

.kuning {
    background: #feb019d9 !important;
    color: white !important;
}

.merah {
    background-color: #c9001dd9 !important;
    color: white !important;
}

.orange {
    background-color: #ff6d00 !important;
    color: white !important;
}

.abu_abu {
    background-color: #33b2ff !important;
    color: white !important;
}

.cream {
    background-color: #9f9ca5 !important;
    color: white !important;
}

.hitam {
    background-color: #000000 !important;
    color: white !important;
}

/* Summary card styling */
.summary-card {
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.performance-badge {
    font-size: 0.9em;
    padding: 0.6em 1.2em;
    border-radius: 20px;
    font-weight: 500;
    display: inline-block;
    transition: all 0.2s ease;
}

.performance-badge:hover {
    transform: scale(1.05);
}

/* Percentage badge styling */
.percentage-badge {
    font-size: 0.9em;
    padding: 0.5em 1em;
    border-radius: 25px;
    font-weight: 600;
    display: inline-block;
    transition: all 0.2s ease;
    text-align: center;
    min-width: 60px;
}

.percentage-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.badge-excellent {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.badge-good {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
}

.badge-average {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.badge-poor {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
}

/* Status badge styling */
.status-badge {
    font-size: 0.8em;
    padding: 0.4em 0.8em;
    border-radius: 15px;
    font-weight: 500;
    display: inline-block;
    transition: all 0.2s ease;
}

.status-badge:hover {
    transform: scale(1.05);
}

/* DPJP Status Classes */
.tepat-waktu {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
}

.terlambat {
    background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
    color: white !important;
}

.lebih-awal {
    background: linear-gradient(135deg, #17a2b8, #20c997) !important;
    color: white !important;
}

.tidak-ada-data {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
    color: white !important;
}

/* Clickable card styling */
.clickable-card {
    transition: all 0.3s ease;
    border: 3px solid transparent;
    cursor: pointer;
}

.clickable-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.clickable-card.border-warning {
    border-color: #ffc107 !important;
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.4);
}

.clickable-card.border-warning:hover {
    box-shadow: 0 6px 25px rgba(255, 193, 7, 0.6);
}

/*------------------------------------------------------------
[7. LOGIN PAGE STYLING]
------------------------------------------------------------*/

/* Login page body override */
body.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-family: 'Poppins', sans-serif !important;
    margin: 0 !important;
    padding: 20px !important;
}

.login-wrapper {
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
        opacity: 0.5;
    }

    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.hospital-logo {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    border: 3px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}

.login-header h2 {
    margin: 0 0 10px 0;
    font-weight: 700;
    font-size: 2rem;
    position: relative;
    z-index: 1;
}

.login-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

.content-wrapper {
    padding: 0;
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/*------------------------------------------------------------
[8. USER MANAGEMENT STYLING]
------------------------------------------------------------*/

.user-card {
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 24px;
    border: none;
    overflow: hidden;
}

.user-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-buttons .btn {
    margin: 3px;
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 0.5rem 0.5rem 0 0;
}

/* Styling khusus untuk card-header di dalam modal */
.modal .card-header {
    background: #f8f9fa;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    font-size: 1rem;
    padding: 15px 20px;
}

/* Hover effect untuk card di dalam modal */
.modal .card {
    transition: all 0.2s ease;
}

.modal .card:hover {
    transform: none; /* Disable transform in modal */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/*------------------------------------------------------------
[9. RESPONSIVE DESIGN]
------------------------------------------------------------*/

@media (max-width: 991.98px) {
    .navbar-brand {
        font-size: 1.2rem;
    }

    /* Navbar toggler styling untuk tablet/desktop kecil */
    .navbar-toggler {
        border: 2px solid rgba(255, 255, 255, 0.5);
        padding: 0.4rem 0.6rem;
        border-radius: 0.375rem;
        background-color: transparent;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        outline: none;
    }

    .navbar-toggler:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        width: 1.5em;
        height: 1.5em;
    }

    /* Navbar collapse container untuk tablet/desktop kecil */
    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, rgba(13, 110, 253, 0.98) 0%, rgba(25, 135, 84, 0.98) 100%);
        border-radius: 0 0 1rem 1rem;
        margin: 0;
        padding: 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-top: none;
        z-index: 1025;
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .navbar-collapse.show {
        display: block !important;
        opacity: 1;
        transform: translateY(0);
    }

    .navbar-collapse.collapsing {
        height: auto !important;
        transition: all 0.3s ease !important;
    }

    /* Disable Bootstrap's default collapse behavior */
    .navbar-collapse:not(.show) {
        display: none !important;
    }

    /* Navbar nav styling untuk tablet/desktop kecil */
    .navbar-nav {
        width: 100%;
        padding: 1rem;
        margin: 0;
        flex-direction: column !important;
    }

    .navbar-nav .nav-item {
        margin: 0.3rem 0;
    }

    .navbar-nav .nav-link {
        padding: 1rem 1.2rem;
        margin: 0;
        border-radius: 0.5rem;
        background-color: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.25);
        color: white !important;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.25);
        transform: translateX(5px);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .navbar-nav .nav-link i {
        margin-right: 0.8rem;
        font-size: 1.1rem;
        width: 20px;
        text-align: center;
    }

    /* User info dan logout styling untuk tablet/desktop kecil */
    .navbar-collapse .d-flex {
        flex-direction: column !important;
        width: 100%;
        padding: 0 1rem 1rem 1rem;
    }

    .navbar-collapse .user-info {
        padding: 0.8rem 1rem;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        text-align: center;
        color: white;
        font-weight: 500;
        margin-bottom: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .navbar-collapse .logout-btn {
        padding: 1rem;
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(185, 28, 28, 0.9) 100%);
        border-radius: 0.5rem;
        text-align: center;
        text-decoration: none;
        color: white !important;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .navbar-collapse .logout-btn:hover {
        background: linear-gradient(135deg, rgba(220, 53, 69, 1) 0%, rgba(185, 28, 28, 1) 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    /* Login responsive */
    body.login-page {
        padding: 10px !important;
    }

    .login-wrapper {
        max-width: 100%;
        margin: 0;
    }

    .login-container {
        border-radius: 15px;
        margin: 0;
        width: 100%;
    }

    .login-header {
        padding: 25px 20px;
    }

    .login-header h2 {
        font-size: 1.5rem;
    }

    .login-header p {
        font-size: 0.85rem;
    }

    .hospital-logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .dashboard-welcome h3 {
        font-size: 1.8rem;
    }

    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Ensure main content doesn't overlap */
    .main-content {
        position: relative;
        z-index: 1;
    }

    /* Warning badge styling untuk tablet/desktop kecil */
    .navbar-nav .nav-link .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
        margin-left: 0.5rem;
    }
}

@media (max-width: 767.98px) {
    .navbar {
        min-height: 60px;
        padding: 0.5rem 0;
        position: relative;
        z-index: 1030;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-brand i {
        font-size: 1.3rem;
    }

    /* Navbar toggler styling */
    .navbar-toggler {
        border: 2px solid rgba(255, 255, 255, 0.5);
        padding: 0.4rem 0.6rem;
        border-radius: 0.375rem;
        background-color: transparent;
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        outline: none;
    }

    .navbar-toggler:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        width: 1.5em;
        height: 1.5em;
    }

    /* Navbar collapse container */
    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, rgba(13, 110, 253, 0.98) 0%, rgba(25, 135, 84, 0.98) 100%);
        border-radius: 0 0 1rem 1rem;
        margin: 0;
        padding: 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-top: none;
        z-index: 1025;
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .navbar-collapse.show {
        display: block !important;
        opacity: 1;
        transform: translateY(0);
    }

    .navbar-collapse.collapsing {
        height: auto !important;
        transition: all 0.3s ease !important;
    }

    /* Disable Bootstrap's default collapse behavior */
    .navbar-collapse:not(.show) {
        display: none !important;
    }

    /* Navbar nav styling */
    .navbar-nav {
        width: 100%;
        padding: 1rem;
        margin: 0;
    }

    .navbar-nav .nav-item {
        margin: 0.3rem 0;
    }

    .navbar-nav .nav-link {
        padding: 1rem 1.2rem;
        margin: 0;
        border-radius: 0.5rem;
        background-color: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.25);
        color: white !important;
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .navbar-nav .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.25);
        transform: translateX(5px);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .navbar-nav .nav-link i {
        margin-right: 0.8rem;
        font-size: 1.1rem;
        width: 20px;
        text-align: center;
    }

    /* User info section in mobile */
    .navbar-collapse .d-flex.align-items-center {
        flex-direction: column;
        align-items: stretch !important;
        margin: 0;
        padding: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.3);
        background-color: rgba(0, 0, 0, 0.1);
    }

    .navbar-collapse .user-info {
        margin-bottom: 0.8rem;
        padding: 0.8rem;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 0.5rem;
        text-align: center;
        color: white;
        font-weight: 500;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .navbar-collapse .logout-btn {
        padding: 1rem;
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.9) 0%, rgba(185, 28, 28, 0.9) 100%);
        border-radius: 0.5rem;
        text-align: center;
        text-decoration: none;
        color: white !important;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .navbar-collapse .logout-btn:hover {
        background: linear-gradient(135deg, rgba(220, 53, 69, 1) 0%, rgba(185, 28, 28, 1) 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    /* Ensure main content doesn't overlap */
    .main-content {
        position: relative;
        z-index: 1;
    }

    /* Warning badge styling for mobile */
    .navbar-nav .nav-link .badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.6rem;
        margin-left: 0.5rem;
    }
}

.dashboard-welcome {
    padding: 2rem 0;
}

.dashboard-welcome h3 {
    font-size: 1.5rem;
}

.dt-buttons {
    justify-content: center;
}

.export-buttons {
    justify-content: center;
}

/* Login responsive mobile */
body.login-page {
    padding: 5px !important;
}

.login-container {
    border-radius: 12px;
    margin: 0;
    width: 100%;
}

.login-header {
    padding: 20px 15px;
}

.login-header h2 {
    font-size: 1.3rem;
}

.login-header p {
    font-size: 0.8rem;
}

.hospital-logo {
    width: 50px;
    height: 50px;
    font-size: 20px;
}

@media (max-width: 575.98px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }

    .dashboard-welcome h3 {
        font-size: 1.3rem;
    }

    .dashboard-welcome p {
        font-size: 1rem;
    }

    /* Login responsive small */
    body.login-page {
        padding: 2px !important;
    }

    .login-container {
        border-radius: 10px;
    }

    .login-header {
        padding: 15px 10px;
    }

    .login-header h2 {
        font-size: 1.1rem;
    }

    .login-header p {
        font-size: 0.75rem;
    }

    .hospital-logo {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

/* Landscape orientation untuk mobile */
@media (max-height: 600px) and (orientation: landscape) {
    body.login-page {
        padding: 5px !important;
    }

    .login-container {
        margin: 5px auto;
        max-height: 95vh;
        overflow-y: auto;
    }

    .login-header {
        padding: 15px 20px;
    }

    .hospital-logo {
        width: 45px;
        height: 45px;
        font-size: 18px;
        margin-bottom: 10px;
    }

    .login-header h2 {
        font-size: 1.2rem;
        margin-bottom: 5px;
    }

    .login-header p {
        font-size: 0.75rem;
    }
}

/*------------------------------------------------------------
[10. ADDITIONAL UI/UX IMPROVEMENTS]
------------------------------------------------------------*/

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus states for accessibility */
*:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Button focus improvements */
.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Tooltip improvements */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #2c3e50;
    border-radius: 6px;
    padding: 8px 12px;
}

/* Selection styling */
::selection {
    background-color: rgba(0, 123, 255, 0.2);
    color: #2c3e50;
}

::-moz-selection {
    background-color: rgba(0, 123, 255, 0.2);
    color: #2c3e50;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Print styles */
@media print {

    .navbar,
    .dt-buttons,
    .export-buttons,
    .btn,
    .loading-overlay {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error states */
.has-error .form-control {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success states */
.has-success .form-control {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.success-message {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}