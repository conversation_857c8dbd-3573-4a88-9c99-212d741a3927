/*!
 DataTables Foundation integration
 ©2011-2015 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,d){a||(a=window);d&&d.fn.dataTable||(d=require("datatables.net")(a,d).$);return c(d,a,a.document)}:c(jQuery,window,document)})(function(c,a,d,z){var e=c.fn.dataTable;a=c('<meta class="foundation-mq"/>').appendTo("head");e.ext.foundationVersion=a.css("font-family").match(/small|medium|large/)?6:5;a.remove();c.extend(e.ext.classes,
{sWrapper:"dataTables_wrapper dt-foundation",sProcessing:"dataTables_processing panel callout"});c.extend(!0,e.defaults,{dom:"<'row grid-x'<'small-6 columns cell'l><'small-6 columns cell'f>r>t<'row grid-x'<'small-6 columns cell'i><'small-6 columns cell'p>>",renderer:"foundation"});e.ext.renderer.pageButton.foundation=function(a,d,u,n,f,l){var q=new e.Api(a),v=a.oClasses,m=a.oLanguage.oPaginate,w=a.oLanguage.oAria.paginate||{},g,k,h,x=5===e.ext.foundationVersion,t=function(d,e){var r,n=function(a){a.preventDefault();
c(a.currentTarget).hasClass("unavailable")||q.page()==a.data.action||q.page(a.data.action).draw("page")};var p=0;for(r=e.length;p<r;p++){var b=e[p];if(c.isArray(b))t(d,b);else{k=g="";h=null;switch(b){case "ellipsis":g="&#x2026;";k="unavailable disabled";h=null;break;case "first":g=m.sFirst;k=b+(0<f?"":" unavailable disabled");h=0<f?"a":null;break;case "previous":g=m.sPrevious;k=b+(0<f?"":" unavailable disabled");h=0<f?"a":null;break;case "next":g=m.sNext;k=b+(f<l-1?"":" unavailable disabled");h=f<
l-1?"a":null;break;case "last":g=m.sLast;k=b+(f<l-1?"":" unavailable disabled");h=f<l-1?"a":null;break;default:g=b+1,k=f===b?"current":"",h=f===b?null:"a"}x&&(h="a");if(g){var y=c("<li>",{"class":v.sPageButton+" "+k,"aria-controls":a.sTableId,"aria-label":w[b],tabindex:a.iTabIndex,id:0===u&&"string"===typeof b?a.sTableId+"_"+b:null}).append(h?c("<"+h+"/>",{href:"#"}).html(g):g).appendTo(d);a.oApi._fnBindAction(y,{action:b},n)}}}};t(c(d).empty().html('<ul class="pagination"/>').children("ul"),n)};
return e});
