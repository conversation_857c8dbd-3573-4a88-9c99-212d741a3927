<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Data Stockart</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="stockartTable" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th><PERSON><PERSON></th>
                                    <th>NORM</th>
                                    <th>No. Kunjungan</th>
                                    <th>DPJP</th>
                                    <th>Dibuat Pada</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Stockart -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">Detail Items Pengajuan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Informasi Pengajuan -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">Informasi Pengajuan</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span id="statusPengajuan">-</span></p>
                                <p><strong>Dibuat Oleh:</strong> <span id="dibuatOleh">-</span></p>
                                <p><strong>Tanggal Dibuat:</strong> <span id="tanggalDibuat">-</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Diupdate Oleh:</strong> <span id="diupdateOleh">-</span></p>
                                <p><strong>Tanggal Update:</strong> <span id="tanggalUpdate">-</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Tabel Detail Items -->
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="detailTable">
                        <thead>
                            <tr>
                                <th>Nama Barang</th>
                                <th>Quantity</th>
                                <th>Note</th>
                            </tr>
                        </thead>
                        <tbody id="detailTableBody">
                            <!-- Data akan dimuat via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" id="btnOrder" onclick="processOrder()">Order</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
<script>
// Global variable to store current id_pengajuan
var currentIdPengajuan = null;

// Function to format date
function formatDate(dateString) {
    if (!dateString || dateString === '-') return '-';
    
    try {
        var date = new Date(dateString);
        var options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };
        return date.toLocaleDateString('id-ID', options);
    } catch (e) {
        return dateString;
    }
}

// Wait for jQuery to be available
function initStockartTable() {
    if (typeof $ === 'undefined') {
        console.log('jQuery not loaded yet, waiting...');
        setTimeout(initStockartTable, 100);
        return;
    }
    
    $(document).ready(function() {
        console.log('Initializing Stockart DataTable...');
        console.log('AJAX URL:', "<?= base_url('Stockart/get_data') ?>");
        
        $('#stockartTable').DataTable({
            processing: true,
            serverSide: false,
            dom: 'Bfrtip',
            buttons: [
                {
                    text: '<i class="fas fa-file-excel"></i> Export Excel',
                    className: 'btn btn-success btn-sm',
                    action: function(e, dt, node, config) {
                        exportToExcel();
                    }
                }
            ],
            ajax: {
                url: '<?php echo base_url("index.php/Stockart/get_data"); ?>',
                type: 'POST',
                error: function(xhr, error, thrown) {
                    console.error('AJAX Error:', error, thrown);
                    console.error('Response:', xhr.responseText);
                }
            },
            columns: [
                { title: 'Nama Pasien' },
                { title: 'NORM' },
                { title: 'No. Kunjungan' },
                { title: 'DPJP' },
                { title: 'Dibuat Pada' },
                { title: 'Status' },
                { title: 'Action', orderable: false, searchable: false },
                { title: 'Status Raw', visible: false } // Hidden column for sorting
            ],
            order: [[ 7, 'desc' ], [ 4, 'desc' ]], // Sort by Status Raw desc, then Dibuat Pada desc
            columnDefs: [
                {
                    targets: 7, // Status Raw column (hidden, for sorting)
                    type: 'num',
                    render: function(data, type, row) {
                        if (type === 'sort' || type === 'type') {
                            // Return numeric value for sorting: status 1 gets highest priority
                            return data == 1 ? 999 : parseInt(data);
                        }
                        return data;
                    }
                }
            ],
            language: {
                processing: "Memproses...",
                search: "Cari:",
                lengthMenu: "Tampilkan _MENU_ entri",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 entri",
                infoFiltered: "(disaring dari _MAX_ total entri)",
                loadingRecords: "Memuat...",
                zeroRecords: "Tidak ada data yang cocok",
                emptyTable: "Tidak ada data tersedia dalam tabel",
                paginate: {
                    first: "Pertama",
                    previous: "Sebelumnya",
                    next: "Selanjutnya",
                    last: "Terakhir"
                }
            }
        });
        
        // Hapus kode berikut:
        // Add click handler for status column header to sort by hidden status raw column
        // $('#stockartTable thead th:eq(4)').on('click', ... )
        // $('#stockartTable thead th:eq(4)').addClass('sorting-desc');
        
        // Initialize status column sorting indicator
        $('#stockartTable thead th:eq(4)').addClass('sorting-desc');
        
        // Function to view detail
         window.viewDetail = function(id_pengajuan) {
             currentIdPengajuan = id_pengajuan; // Store current id_pengajuan
             $.ajax({
                 url: '<?php echo base_url("index.php/Stockart/get_detail"); ?>',
                 type: 'POST',
                 data: { id_pengajuan: id_pengajuan },
                 dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // Populate informasi pengajuan
                        if (response.pengajuan_info) {
                            $('#dibuatOleh').text(response.pengajuan_info.dibuat_oleh || '-');
                            $('#tanggalDibuat').text(formatDate(response.pengajuan_info.created_at));
                            $('#diupdateOleh').text(response.pengajuan_info.diupdate_oleh || '-');
                            $('#tanggalUpdate').text(formatDate(response.pengajuan_info.updated_at));
                            
                            // Set status badge
                            var status = parseInt(response.pengajuan_info.status);
                            var statusBadge = '';
                            switch(status) {
                                case 1:
                                    statusBadge = '<span class="badge bg-warning">Diajukan</span>';
                                    break;
                                case 2:
                                    statusBadge = '<span class="badge bg-success">Diterima</span>';
                                    break;
                                case 3:
                                    statusBadge = '<span class="badge bg-danger">Ditolak</span>';
                                    break;
                                default:
                                    statusBadge = '<span class="badge bg-secondary">Unknown</span>';
                            }
                            $('#statusPengajuan').html(statusBadge);
                            
                            // Hide/show order button based on status
                            // Status 2 = sudah diterima/ordered, jadi tombol order disembunyikan
                            if (status === 2) {
                                $('#btnOrder').hide();
                            } else {
                                $('#btnOrder').show();
                            }
                        }
                        
                        // Populate tabel detail items
                        var tbody = $('#detailTableBody');
                        tbody.empty();
                        
                        $.each(response.data, function(index, item) {
                            var row = '<tr>' +
                                '<td>' + item.nama_barang + '</td>' +
                                '<td>' + item.quantity + '</td>' +
                                '<td>' + (item.note || '-') + '</td>' +
                                '</tr>';
                            tbody.append(row);
                        });
                        
                        $('#detailModal').modal('show');
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: response.message || 'Terjadi kesalahan saat memuat detail.'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: 'Terjadi kesalahan saat memuat detail.'
                    });
                }
            });
        };
        
        // Function to export to Excel
        window.exportToExcel = function() {
            Swal.fire({
                title: 'Export Excel',
                text: 'Sedang memproses export data...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '<?php echo base_url("index.php/Stockart/export_excel"); ?>',
                type: 'POST',
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    // Detect content type from response
                    var contentType = xhr.getResponseHeader('Content-Type');
                    var blob;
                    var defaultFilename;

                    if (contentType && (contentType.includes('ms-excel') || contentType.includes('spreadsheetml'))) {
                        // Excel file
                        blob = new Blob([data], {
                            type: 'application/vnd.ms-excel'
                        });
                        defaultFilename = 'Detail_Items_Stockart_' + new Date().toISOString().slice(0,10) + '.xls';
                    } else {
                        // CSV fallback
                        blob = new Blob([data], {
                            type: 'text/csv'
                        });
                        defaultFilename = 'Detail_Items_Stockart_' + new Date().toISOString().slice(0,10) + '.csv';
                    }

                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);

                    // Get filename from response header or use default
                    var filename = defaultFilename;
                    var disposition = xhr.getResponseHeader('Content-Disposition');
                    if (disposition && disposition.indexOf('attachment') !== -1) {
                        var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                        var matches = filenameRegex.exec(disposition);
                        if (matches != null && matches[1]) {
                            filename = matches[1].replace(/['"]/g, '');
                        }
                    }

                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    var message = (filename.endsWith('.xls') || filename.endsWith('.xlsx')) ?
                        'Data berhasil diexport ke Excel!' :
                        'Data berhasil diexport ke Excel (CSV format)!';

                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: message
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Export Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: 'Terjadi kesalahan saat export data.'
                    });
                }
            });
        };

        // Function to process order
        window.processOrder = function() {
            if (!currentIdPengajuan) {
                Swal.fire({
                    icon: 'error',
                    title: 'ID Tidak Ditemukan',
                    text: 'ID Pengajuan tidak ditemukan'
                });
                return;
            }
            
            Swal.fire({
                title: 'Konfirmasi Order',
                text: 'Apakah Anda yakin ingin membuat order untuk pengajuan ini?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Ya, Order',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (!result.isConfirmed) return;
                // Disable button to prevent double click
                $('#btnOrder').prop('disabled', true).text('Processing...');
                $.ajax({
                    url: '<?php echo base_url("index.php/Stockart/insertOrder"); ?>',
                    type: 'POST',
                    data: { id_pengajuan: currentIdPengajuan },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil',
                                text: response.message || 'Order berhasil diajukan.'
                            });
                            $('#detailModal').modal('hide');
                            // Refresh the main table
                            $('#stockartTable').DataTable().ajax.reload();
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal',
                                text: response.message || 'Terjadi kesalahan saat memproses order.'
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal',
                            text: 'Terjadi kesalahan saat memproses order.'
                        });
                    },
                    complete: function() {
                        // Re-enable button
                        $('#btnOrder').prop('disabled', false).text('Order');
                    }
                });
            });
        };
        
        // Function to reset modal content
        function resetModalContent() {
            $('#statusPengajuan').text('-');
            $('#dibuatOleh').text('-');
            $('#tanggalDibuat').text('-');
            $('#diupdateOleh').text('-');
            $('#tanggalUpdate').text('-');
            $('#detailTableBody').empty();
            $('#btnOrder').show(); // Show order button by default
            currentIdPengajuan = null;
        }
        
        // Manual event handlers for modal close
        $(document).on('click', '[data-dismiss="modal"]', function() {
            $('#detailModal').modal('hide');
        });
        
        // Close modal when clicking outside
        $('#detailModal').on('click', function(e) {
            if (e.target === this) {
                $(this).modal('hide');
            }
        });
        
        // Close modal with ESC key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && $('#detailModal').hasClass('show')) {
                $('#detailModal').modal('hide');
            }
        });
        
        // Reset content when modal is hidden
        $('#detailModal').on('hidden.bs.modal', function() {
            resetModalContent();
        });
    });
}

// Start initialization
initStockartTable();
</script>