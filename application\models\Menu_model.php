<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Menu_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Ambil menu berdasarkan user ID
     */
    public function get_user_menu($user_id) {
        $this->db->select('
            m.ID,
            m.LABEL,
            m.LINK,
            m.PARENT,
            m.SEQ,
            m.ICON
        ');
        $this->db->from('aplikasi.towerc_menu m');
        $this->db->join('aplikasi.towerc_menu_user mu', 'm.ID = mu.ID_MENU');
        $this->db->where('mu.ID_USER', $user_id);
        $this->db->where('m.STATUS', 1);
        $this->db->where('mu.STATUS', 1);
        $this->db->order_by('m.SEQ', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil semua menu
     */
    public function get_all_menu() {
        $this->db->select('*');
        $this->db->from('aplikasi.towerc_menu');
        $this->db->where('STATUS', 1);
        $this->db->order_by('SEQ', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil menu dengan informasi akses user
     */
    public function get_menu_with_user_access($user_id = null) {
        if ($user_id) {
            // Ambil semua menu
            $this->db->select('m.*');
            $this->db->from('aplikasi.towerc_menu m');
            $this->db->where('m.STATUS', 1);
            $this->db->order_by('m.SEQ', 'ASC');
            $menus = $this->db->get()->result_array();

            // Ambil menu yang sudah diakses user
            $this->db->select('ID_MENU');
            $this->db->from('aplikasi.towerc_menu_user');
            $this->db->where('ID_USER', $user_id);
            $this->db->where('STATUS', 1);
            $user_menu_access = $this->db->get()->result_array();

            // Convert ke array ID untuk mudah dicek
            $accessed_menu_ids = array_column($user_menu_access, 'ID_MENU');

            // Tambahkan flag has_access ke setiap menu
            foreach ($menus as &$menu) {
                $menu['has_access'] = in_array($menu['ID'], $accessed_menu_ids) ? 1 : 0;
            }

            return $menus;
        } else {
            // Jika tidak ada user_id, ambil semua menu tanpa info akses
            $this->db->select('m.*, 0 as has_access');
            $this->db->from('aplikasi.towerc_menu m');
            $this->db->where('m.STATUS', 1);
            $this->db->order_by('m.SEQ', 'ASC');

            $query = $this->db->get();
            return $query->result_array();
        }
    }

    /**
     * Simpan menu baru
     */
    public function save_menu($data) {
        $menu_data = array(
            'LABEL' => $data['label'],
            'LINK' => $data['link'],
            'PARENT' => isset($data['parent']) ? $data['parent'] : 0,
            'SEQ' => isset($data['seq']) ? $data['seq'] : $this->get_next_sequence(),
            'ICON' => isset($data['icon']) ? $data['icon'] : 'fa-circle',
            'STATUS' => 1,
            'CREATED_AT' => date('Y-m-d H:i:s')
        );

        $this->db->insert('aplikasi.towerc_menu', $menu_data);
        return $this->db->insert_id();
    }

    /**
     * Update menu
     */
    public function update_menu($menu_id, $data) {
        $menu_data = array(
            'UPDATED_AT' => date('Y-m-d H:i:s')
        );

        if (isset($data['label'])) $menu_data['LABEL'] = $data['label'];
        if (isset($data['link'])) $menu_data['LINK'] = $data['link'];
        if (isset($data['parent'])) $menu_data['PARENT'] = $data['parent'];
        if (isset($data['seq'])) $menu_data['SEQ'] = $data['seq'];
        if (isset($data['icon'])) $menu_data['ICON'] = $data['icon'];
        if (isset($data['status'])) $menu_data['STATUS'] = $data['status'];

        $this->db->where('ID', $menu_id);
        return $this->db->update('aplikasi.towerc_menu', $menu_data);
    }

    /**
     * Hapus menu (soft delete)
     */
    public function delete_menu($menu_id) {
        return $this->update_menu($menu_id, array('status' => 0));
    }

    /**
     * Set akses menu untuk user
     */
    public function set_user_menu_access($user_id, $menu_ids) {
        // Mulai transaction untuk memastikan data consistency
        $this->db->trans_start();

        try {
            // Ambil semua menu yang tersedia
            $all_menus = $this->get_all_menu();
            $current_time = date('Y-m-d H:i:s');

            foreach ($all_menus as $menu) {
                $menu_id = $menu['ID'];
                $has_access = in_array($menu_id, $menu_ids) ? 1 : 0;

                // Cek apakah record sudah ada
                $existing = $this->db->select('ID')
                    ->from('aplikasi.towerc_menu_user')
                    ->where('ID_USER', $user_id)
                    ->where('ID_MENU', $menu_id)
                    ->get()
                    ->row();

                if ($existing) {
                    // Update existing record - SET status sesuai checkbox
                    $this->db->where('ID_USER', $user_id)
                        ->where('ID_MENU', $menu_id)
                        ->update('aplikasi.towerc_menu_user', [
                            'STATUS' => $has_access,
                            'UPDATED_AT' => $current_time
                        ]);
                } else if ($has_access == 1) {
                    // Insert new record hanya jika user memiliki akses
                    // Tidak perlu insert record dengan STATUS = 0
                    $this->db->insert('aplikasi.towerc_menu_user', [
                        'ID_MENU' => $menu_id,
                        'ID_USER' => $user_id,
                        'STATUS' => 1,
                        'CREATED_AT' => $current_time,
                        'UPDATED_AT' => $current_time
                    ]);
                }
            }

            // Complete transaction
            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                return false;
            }

            return true;

        } catch (Exception $e) {
            $this->db->trans_rollback();
            log_message('error', 'Error in set_user_menu_access: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Ambil akses menu user
     */
    public function get_user_menu_access($user_id) {
        $this->db->select('ID_MENU');
        $this->db->from('aplikasi.towerc_menu_user');
        $this->db->where('ID_USER', $user_id);
        $this->db->where('STATUS', 1);
        
        $query = $this->db->get();
        $result = $query->result_array();
        
        return array_column($result, 'ID_MENU');
    }

    /**
     * Ambil semua user
     */
    public function get_all_users() {
        $this->db->select('ID, LOGIN, NAMA');
        $this->db->from('aplikasi.pengguna');
        $this->db->where('STATUS', 1);
        $this->db->order_by('NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil semua user dengan jumlah menu access
     */
    public function get_users_with_menu_count() {
        $this->db->select('
            u.ID,
            u.LOGIN,
            u.NAMA,
            COUNT(mu.ID_MENU) as total_menu_access
        ');
        $this->db->from('aplikasi.pengguna u');
        $this->db->join('aplikasi.towerc_menu_user mu', 'u.ID = mu.ID_USER', 'left');
        $this->db->where('u.STATUS', 1);
        $this->db->where('(mu.STATUS = 1 OR mu.STATUS IS NULL)');
        $this->db->group_by('u.ID, u.LOGIN, u.NAMA');
        $this->db->order_by('u.NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil user dengan informasi akses menu
     */
    public function get_user_with_menu_access_detail($user_id) {
        $this->db->select('
            u.ID,
            u.LOGIN,
            u.NAMA,
            COUNT(mu.ID) as total_menu_access
        ');
        $this->db->from('aplikasi.pengguna u');
        $this->db->join('aplikasi.towerc_menu_user mu', 'u.ID = mu.ID_USER AND mu.STATUS = 1', 'left');
        $this->db->where('u.STATUS', 1);
        $this->db->group_by('u.ID, u.LOGIN, u.NAMA');
        $this->db->order_by('u.NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil data user dengan akses menu berdasarkan user ID
     */
    public function get_user_with_menu_access($user_id) {
        $this->db->select('
            u.ID,
            u.LOGIN,
            u.NAMA,
            u.STATUS
        ');
        $this->db->from('aplikasi.pengguna u');
        $this->db->where('u.ID', $user_id);
        $this->db->where('u.STATUS', 1);

        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Ambil data users untuk DataTables serverside
     */
    public function get_users_datatables($start, $length, $search_value, $order_column, $order_dir) {
        $this->db->select('
            u.ID,
            u.LOGIN,
            u.NAMA,
            COUNT(mu.ID) as total_menu_access
        ');
        $this->db->from('aplikasi.pengguna u');
        $this->db->join('aplikasi.towerc_menu_user mu', 'u.ID = mu.ID_USER AND mu.STATUS = 1', 'left');
        $this->db->where('u.STATUS', 1);

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('u.LOGIN', $search_value);
            $this->db->or_like('u.NAMA', $search_value);
            $this->db->group_end();
        }

        $this->db->group_by('u.ID, u.LOGIN, u.NAMA');
        $this->db->order_by($order_column, $order_dir);
        $this->db->limit($length, $start);

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Hitung total semua users
     */
    public function count_all_users() {
        $this->db->from('aplikasi.pengguna');
        $this->db->where('STATUS', 1);
        return $this->db->count_all_results();
    }

    /**
     * Hitung users yang difilter
     */
    public function count_filtered_users($search_value) {
        $this->db->from('aplikasi.pengguna u');
        $this->db->where('u.STATUS', 1);

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('u.LOGIN', $search_value);
            $this->db->or_like('u.NAMA', $search_value);
            $this->db->group_end();
        }

        return $this->db->count_all_results();
    }

    /**
     * Hitung users yang memiliki akses menu
     */
    public function count_users_with_access() {
        $this->db->select('COUNT(DISTINCT u.ID) as count');
        $this->db->from('aplikasi.pengguna u');
        $this->db->join('aplikasi.towerc_menu_user mu', 'u.ID = mu.ID_USER AND mu.STATUS = 1');
        $this->db->where('u.STATUS', 1);

        $query = $this->db->get();
        $result = $query->row_array();

        return $result['count'];
    }

    /**
     * Cek apakah user memiliki akses ke menu
     */
    public function check_user_access($user_id, $menu_link) {
        $this->db->select('COUNT(*) as count');
        $this->db->from('aplikasi.towerc_menu m');
        $this->db->join('aplikasi.towerc_menu_user mu', 'm.ID = mu.ID_MENU');
        $this->db->where('mu.ID_USER', $user_id);
        $this->db->where('m.LINK', $menu_link);
        $this->db->where('m.STATUS', 1);
        $this->db->where('mu.STATUS', 1);
        
        $query = $this->db->get();
        $result = $query->row_array();
        
        return $result['count'] > 0;
    }

    /**
     * Ambil sequence berikutnya
     */
    private function get_next_sequence() {
        $this->db->select_max('SEQ');
        $this->db->from('aplikasi.towerc_menu');
        
        $query = $this->db->get();
        $result = $query->row_array();
        
        return ($result['SEQ'] ?: 0) + 1;
    }

    /**
     * Reorder menu sequence
     */
    public function reorder_menu($menu_orders) {
        foreach ($menu_orders as $menu_id => $seq) {
            $this->db->where('ID', $menu_id);
            $this->db->update('aplikasi.towerc_menu', array(
                'SEQ' => $seq,
                'UPDATED_AT' => date('Y-m-d H:i:s')
            ));
        }
        
        return true;
    }

    /**
     * Cek apakah tabel menu sudah ada
     */
    public function check_menu_tables_exist() {
        $tables = array(
            'aplikasi.towerc_menu',
            'aplikasi.towerc_menu_user'
        );
        
        $existing_tables = array();
        foreach ($tables as $table) {
            if ($this->db->table_exists($table)) {
                $existing_tables[] = $table;
            }
        }
        
        return array(
            'required' => $tables,
            'existing' => $existing_tables,
            'missing' => array_diff($tables, $existing_tables),
            'all_exist' => count($existing_tables) == count($tables)
        );
    }

    /**
     * Install default menu data
     */
    public function install_default_menu() {
        $default_menus = array(
            array('LABEL' => 'Data Perjanjian', 'LINK' => 'Perjanjian', 'PARENT' => 0, 'SEQ' => 1, 'ICON' => 'fa-calendar-check'),
            array('LABEL' => 'Ketepatan DPJP', 'LINK' => 'RekapPerDokter', 'PARENT' => 0, 'SEQ' => 2, 'ICON' => 'fa-clock'),
            array('LABEL' => 'Hasil Patologi Anatomi', 'LINK' => 'MonitoringPaCendana', 'PARENT' => 0, 'SEQ' => 3, 'ICON' => 'fa-tachometer-alt'),
            array('LABEL' => 'Stockart', 'LINK' => 'Stockart', 'PARENT' => 0, 'SEQ' => 4, 'ICON' => 'fa-boxes'),
            array('LABEL' => 'Alert Pasien', 'LINK' => 'AlertPasien', 'PARENT' => 0, 'SEQ' => 5, 'ICON' => 'fa-bell'),
            array('LABEL' => 'Admin Menu', 'LINK' => 'AdminMenu', 'PARENT' => 0, 'SEQ' => 6, 'ICON' => 'fa-cog')
        );

        foreach ($default_menus as $menu) {
            $menu['STATUS'] = 1;
            $menu['CREATED_AT'] = date('Y-m-d H:i:s');
            $this->db->insert('aplikasi.towerc_menu', $menu);
        }

        return true;
    }

}
