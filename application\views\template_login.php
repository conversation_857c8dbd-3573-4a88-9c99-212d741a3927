<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="Login - Monitoring DPJP Praktek" />
    <meta name="author" content="" />
    <title>Login - Monitoring DPJP Praktek</title>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/bootstrap/css/bootstrap.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/font-awesome/css/all.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/main.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/dashboard.css') ?>" rel="stylesheet">
</head>



<body class="login-page">
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="login-wrapper">
        <div class="login-container">
            <div class="login-header">
                <div class="hospital-logo">
                    <i class="fas fa-hospital"></i>
                </div>
                <h2>MONITORING DPJP</h2>
                <p>Sistem Monitoring DPJP Praktek</p>
            </div>
            
            <div class="content-wrapper">
                <?php 
                if(isset($contents)) {
                    echo $contents;
                } else {
                    $this->load->view('form_login');
                }
                ?>
            </div>
        </div>
    </div>

    <script src="<?= base_url('assets/plugins/jquery/jquery-3.4.1.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/bootstrap/js/bootstrap.min.js') ?>"></script>
    
    <script>
        $(document).ready(function() {
            // Hide loading overlay when page is ready
            $('#loadingOverlay').fadeOut();
            
            // Show loading overlay on form submit
            $('form').on('submit', function() {
                $('#loadingOverlay').fadeIn();
            });
            
            // Auto-hide alerts after 5 seconds
            $('.alert').delay(5000).fadeOut();
            
            // Add some interactive effects
            $('.login-container').hover(
                function() {
                    $(this).css('transform', 'translateY(-5px)');
                },
                function() {
                    $(this).css('transform', 'translateY(0)');
                }
            );
        });
    </script>
</body>
</html>
