<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

    function __construct() {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->helper('url');
        $this->load->library('session');
        
        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth/login');
        }
    }

    public function index() {
        $data['title'] = 'Dashboard';
        $data['page_content'] = 'dashboard';
        
        // Ambil statistik untuk dashboard
        $data['user_info'] = array(
            'nama' => $this->session->userdata('nama'),
            'username' => $this->session->userdata('username'),
            'login_time' => date('d/m/Y H:i:s')
        );
        
        // Cek apakah user memiliki akses menu
        $user_id = $this->session->userdata('id');
        try {
            $user_menus = $this->Menu_model->get_user_menu($user_id);
            $data['user_menus'] = $user_menus;
            $data['has_menu_access'] = !empty($user_menus);
        } catch (Exception $e) {
            $data['user_menus'] = array();
            $data['has_menu_access'] = false;
        }
        
        $this->load->view('template_dashboard', $data);
    }

    public function welcome() {
        // Redirect ke index
        redirect('Dashboard');
    }
}
