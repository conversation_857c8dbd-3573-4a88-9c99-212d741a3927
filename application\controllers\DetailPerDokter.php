<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DetailPerDokter extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->helper('url');
        $this->load->model('Model_pulang');
        $this->load->library('session');
        
        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth_fixed/login');
        }
    }

    public function index() {
        $this->load->view('v_detail_per_dokter');
    }

    public function test_connection() {
        echo "Controller is working! Current time: " . date('Y-m-d H:i:s');
    }

    public function get_detail_per_dokter_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            // Debug: Log the parameters
            log_message('debug', 'Detail Per Dokter - tglAwal: ' . $tglAwal . ', tglAkhir: ' . $tglAkhir);

            $listData = $this->Model_pulang->detail_per_dokter_new($tglAwal, $tglAkhir);

            if (!$listData) {
                throw new Exception('Query failed or returned null');
            }

            $data = array();
            $totalRows = $listData->num_rows();

            // Debug: Log the number of rows
            log_message('debug', 'Detail Per Dokter - Total rows: ' . $totalRows);

            if ($totalRows > 0) {
                foreach ($listData->result() as $row) {
                    $data[] = array(
                        $row->DOKTERID ?? '',
                        $row->DOKTER ?? '',
                        $row->RUANGAN_PRAKTEK ?? '',
                        $row->RUANGAN_PERJANJIAN ?? '',
                        $row->TANGGAL_PENDAFTARAN ?? '',
                        $row->TANGGAL_PERJANJIAN ?? '',
                        $row->JADWAL_MULAI_PRAKTEK ?? '',
                        $row->JAM_BOARDING ?? '',
                        $row->JAM_MULAI ?? '',
                        $row->JAM_CPPT ?? '',
                        $row->DURASI ?? '',
                        $row->KEDATANGAN ?? ''
                    );
                }
            }

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => $totalRows,
                "recordsFiltered" => $totalRows,
                "data" => $data
            );

            echo json_encode($output);

        } catch (Exception $e) {
            log_message('error', 'Detail Per Dokter Error: ' . $e->getMessage());

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            );

            echo json_encode($output);
        }
    }

    public function get_summary_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            $listData = $this->Model_pulang->detail_per_dokter_new($tglAwal, $tglAkhir);

            $summary = array(
                'total_dokter' => 0,
                'total_records' => 0,
                'total_dengan_appointment' => 0,
                'total_tanpa_appointment' => 0,
                'total_dengan_data' => 0,
                'total_tanpa_data' => 0
            );

            if ($listData && $listData->num_rows() > 0) {
                $summary['total_records'] = $listData->num_rows();
                $dokter_list = array();

                foreach ($listData->result() as $row) {
                    // Count unique doctors
                    if (!in_array($row->DOKTERID, $dokter_list)) {
                        $dokter_list[] = $row->DOKTERID;
                    }

                    // Count appointment status
                    if ($row->TANGGAL_PERJANJIAN != 'TIDAK ADA APPOINTMENT') {
                        $summary['total_dengan_appointment']++;
                    } else {
                        $summary['total_tanpa_appointment']++;
                    }

                    // Count data availability
                    if ($row->JAM_MULAI != 'TIDAK ADA DATA') {
                        $summary['total_dengan_data']++;
                    } else {
                        $summary['total_tanpa_data']++;
                    }
                }

                $summary['total_dokter'] = count($dokter_list);
            }

            echo json_encode($summary);

        } catch (Exception $e) {
            log_message('error', 'Detail Per Dokter Summary Error: ' . $e->getMessage());

            $summary = array(
                'total_dokter' => 0,
                'total_pasien' => 0,
                'total_tepat_waktu' => 0,
                'total_terlambat' => 0,
                'total_lebih_awal' => 0,
                'total_tidak_ada_data' => 0,
                'rata_rata_persentase' => 0,
                'error' => $e->getMessage()
            );

            echo json_encode($summary);
        }
    }



    public function export_excel() {
        // Excel export functionality - to be implemented
        echo "Excel export functionality - coming soon";
    }
}
