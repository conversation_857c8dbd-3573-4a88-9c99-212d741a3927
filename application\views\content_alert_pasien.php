<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🚨 <PERSON>ert <PERSON>sien</h2>
                    <p class="text-muted">Sistem peringatan untuk pasien yang memerlukan perhatian khusus</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if($this->session->flashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $this->session->flashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($this->session->flashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $this->session->flashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($this->session->flashdata('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= $this->session->flashdata('warning') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <div class="text-danger">
                        <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                    </div>
                    <h4 class="text-danger"><?= $alert_stats['kategori']['High'] ?? 0 ?></h4>
                    <p class="mb-0">High Priority</p>
                    <small class="text-muted">Hari ini</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="text-warning">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    </div>
                    <h4 class="text-warning"><?= $alert_stats['kategori']['Medium'] ?? 0 ?></h4>
                    <p class="mb-0">Medium Priority</p>
                    <small class="text-muted">Hari ini</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="text-success">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                    </div>
                    <h4 class="text-success"><?= $alert_stats['kategori']['Low'] ?? 0 ?></h4>
                    <p class="mb-0">Low Priority</p>
                    <small class="text-muted">Hari ini</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="text-primary">
                        <i class="fas fa-bell fa-2x mb-2"></i>
                    </div>
                    <h4 class="text-primary"><?= array_sum(array_column($alert_stats['kategori'], 'total')) ?></h4>
                    <p class="mb-0">Total Alert</p>
                    <small class="text-muted">Hari ini</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form Card -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        Buat Alert Pasien Baru
                    </h5>
                </div>
                <div class="card-body">
                    <form id="alertForm" action="<?= base_url('AlertPasien/submit_alert') ?>" method="POST">
                        <!-- Input No. MR dengan Autocomplete -->
                        <div class="mb-3">
                            <label for="norm_input" class="form-label">
                                <i class="fas fa-id-card me-1"></i>
                                No. MR (Medical Record) <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="norm_input" 
                                   placeholder="Ketik NORM atau nama pasien..."
                                   autocomplete="off">
                            <input type="hidden" name="norm" id="norm_hidden" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Mulai ketik untuk mencari pasien berdasarkan NORM atau nama
                            </div>
                        </div>

                        <!-- Nama Pasien (Readonly) -->
                        <div class="mb-3">
                            <label for="nama_pasien" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                Nama Pasien <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="nama_pasien" 
                                   name="nama_pasien" 
                                   readonly 
                                   placeholder="Nama pasien akan terisi otomatis"
                                   required>
                        </div>

                        <!-- Kategori Pasien -->
                        <div class="mb-3">
                            <label for="kategori" class="form-label">
                                <i class="fas fa-flag me-1"></i>
                                Kategori Prioritas <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="kategori" name="kategori" required>
                                <option value="">Pilih Kategori</option>
                                <option value="High" selected>🔴 High / Priority</option>
                                <option value="Medium">🟡 Medium</option>
                                <option value="Low">🟢 Low</option>
                            </select>
                        </div>

                        <!-- Keterangan -->
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">
                                <i class="fas fa-comment-medical me-1"></i>
                                Keterangan / Keluhan Pasien <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" 
                                      id="keterangan" 
                                      name="keterangan" 
                                      rows="4" 
                                      placeholder="Tuliskan keluhan atau kondisi pasien yang memerlukan perhatian khusus..."
                                      required></textarea>
                        </div>

                        <!-- Pilih Petugas -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-users me-1"></i>
                                Pilih Petugas Penerima WhatsApp <span class="text-danger">*</span>
                            </label>
                            <div class="border rounded p-3 bg-light">
                                <div class="mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">
                                        <i class="fas fa-check-double me-1"></i>
                                        Pilih Semua
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">
                                        <i class="fas fa-times me-1"></i>
                                        Batal Semua
                                    </button>
                                </div>
                                <?php if(!empty($petugas_list)): ?>
                                    <?php foreach($petugas_list as $petugas): ?>
                                        <div class="form-check">
                                            <input class="form-check-input petugas-checkbox" 
                                                   type="checkbox" 
                                                   name="petugas[]" 
                                                   value="<?= $petugas['ID'] ?>" 
                                                   id="petugas_<?= $petugas['ID'] ?>"
                                                   checked>
                                            <label class="form-check-label" for="petugas_<?= $petugas['ID'] ?>">
                                                <i class="fab fa-whatsapp text-success me-1"></i>
                                                <?= $petugas['NAMA'] ?>
                                                <small class="text-muted">(<?= $petugas['NOMOR_HP'] ?>)</small>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-muted">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Tidak ada data petugas. Silakan hubungi administrator.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                <i class="fas fa-paper-plane me-2"></i>
                                Kirim Alert Pasien
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar Info -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informasi
                    </h6>
                </div>
                <div class="card-body">
                    <h6>📋 Cara Penggunaan:</h6>
                    <ol class="small">
                        <li>Ketik NORM atau nama pasien di kolom pencarian</li>
                        <li>Pilih pasien dari hasil pencarian</li>
                        <li>Tentukan kategori prioritas</li>
                        <li>Isi keterangan/keluhan pasien</li>
                        <li>Pilih petugas yang akan menerima alert</li>
                        <li>Klik "Kirim Alert Pasien"</li>
                    </ol>

                    <hr>

                    <h6>🔔 Kategori Prioritas:</h6>
                    <ul class="small">
                        <li><span class="badge bg-danger">High</span> - Kondisi darurat/kritis</li>
                        <li><span class="badge bg-warning">Medium</span> - Perlu perhatian segera</li>
                        <li><span class="badge bg-success">Low</span> - Informasi umum</li>
                    </ul>

                    <hr>

                    <h6>📱 WhatsApp Alert:</h6>
                    <p class="small text-muted">
                        Pesan akan dikirim otomatis ke petugas yang dipilih melalui WhatsApp dengan format yang sudah ditentukan.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Riwayat Alert Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Riwayat Alert Pasien
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="riwayatTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">No</th>
                                    <th width="15%">Tanggal/Waktu</th>
                                    <th width="15%">NORM</th>
                                    <th width="20%">Nama Pasien</th>
                                    <th width="10%">Kategori</th>
                                    <th width="25%">Keterangan</th>
                                    <th width="10%">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan dimuat via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include jQuery UI CSS for autocomplete -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
<script>
// Wait for all scripts to load before initializing
function initAlertPasienForm() {
    // Check if jQuery and jQuery UI are available
    if (typeof jQuery === 'undefined') {
        console.log('jQuery not loaded yet, retrying...');
        setTimeout(initAlertPasienForm, 100);
        return;
    }

    if (typeof jQuery.ui === 'undefined') {
        console.log('jQuery UI not loaded yet, retrying...');
        setTimeout(initAlertPasienForm, 100);
        return;
    }

    $(document).ready(function() {
        console.log('Initializing Alert Pasien form...');

        // Autocomplete untuk pencarian pasien
        $("#norm_input").autocomplete({
            source: function(request, response) {
                var ajaxUrl = "<?= base_url('AlertPasien/autocomplete_pasien') ?>";
                console.log('AJAX URL:', ajaxUrl);
                console.log('Search term:', request.term);

                $.ajax({
                    url: ajaxUrl,
                    type: 'GET',
                    dataType: "json",
                    data: {
                        term: request.term
                    },
                    beforeSend: function() {
                        console.log('Sending AJAX request...');
                    },
                    success: function(data) {
                        console.log('Autocomplete data received:', data);
                        response(data);
                    },
                    error: function(xhr, status, error) {
                        console.error('Autocomplete AJAX error:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText,
                            url: ajaxUrl
                        });
                        response([]);
                    }
                });
            },
            minLength: 2,
            select: function(event, ui) {
                console.log('Selected item:', ui.item);
                // Set hidden field dan nama pasien
                $("#norm_hidden").val(ui.item.norm);
                $("#nama_pasien").val(ui.item.nama);

                // Update display value
                $(this).val(ui.item.label);

                return false;
            },
            focus: function(event, ui) {
                $(this).val(ui.item.label);
                return false;
            }
        });

        // Auto-fill nama pasien saat user mengetik NORM langsung
        var autoFillTimeout;
        $("#norm_input").on('input', function() {
            var inputValue = $(this).val().trim();

            if(inputValue === '') {
                $("#norm_hidden").val('');
                $("#nama_pasien").val('');
                return;
            }

            // Clear previous timeout
            clearTimeout(autoFillTimeout);

            // Set timeout untuk auto-fill (delay 1 detik setelah user berhenti mengetik)
            autoFillTimeout = setTimeout(function() {
                // Cek apakah input berupa NORM (angka) dan belum ada nama pasien
                if(/^\d+$/.test(inputValue) && $("#nama_pasien").val() === '') {
                    console.log('Attempting auto-fill for NORM:', inputValue);

                    $.ajax({
                        url: "<?= base_url('AlertPasien/get_pasien_by_norm') ?>",
                        type: 'POST',
                        dataType: 'json',
                        data: { norm: inputValue },
                        success: function(response) {
                            console.log('Auto-fill response:', response);
                            if(response.success && response.data) {
                                // Set hidden field dan nama pasien
                                $("#norm_hidden").val(response.data.NORM);
                                $("#nama_pasien").val(response.data.NAMA);

                                // Update display value dengan format yang sama seperti autocomplete
                                $("#norm_input").val(response.data.NORM + ' - ' + response.data.NAMA);

                                console.log('Auto-fill successful for:', response.data.NAMA);
                            } else {
                                console.log('Patient not found for NORM:', inputValue);
                                // Jika tidak ditemukan, biarkan user melanjutkan dengan autocomplete
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Auto-fill error:', error);
                            // Jika error, biarkan user melanjutkan dengan autocomplete
                        }
                    });
                }
            }, 1000); // Delay 1 detik
        });

        // Select/Deselect all petugas - using event delegation
        $(document).on('click', '#selectAll', function(e) {
            e.preventDefault();
            console.log('Select All clicked');
            $(".petugas-checkbox").prop('checked', true);
            console.log('Checked checkboxes:', $(".petugas-checkbox:checked").length);
        });

        $(document).on('click', '#deselectAll', function(e) {
            e.preventDefault();
            console.log('Deselect All clicked');
            $(".petugas-checkbox").prop('checked', false);
            console.log('Checked checkboxes:', $(".petugas-checkbox:checked").length);
        });

        // Form validation dan submit dengan AJAX
        $("#alertForm").submit(function(e) {
            e.preventDefault(); // Prevent default form submission
            console.log('Alert form submit intercepted!');

            var norm = $("#norm_hidden").val();
            var nama = $("#nama_pasien").val();
            var kategori = $("#kategori").val();
            var keterangan = $("#keterangan").val();
            var petugasChecked = $(".petugas-checkbox:checked").length;

            console.log('Form data:', {norm, nama, kategori, keterangan, petugasChecked});

            // Test SweetAlert first
            if (typeof Swal === 'undefined') {
                alert('SweetAlert2 not loaded!');
                return false;
            }

            // Validasi form
            if(!norm || !nama) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Data Tidak Lengkap',
                    text: 'Silakan pilih pasien terlebih dahulu!'
                });
                $("#norm_input").focus();
                return false;
            }

            if(!kategori) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Data Tidak Lengkap',
                    text: 'Silakan pilih kategori prioritas!'
                });
                $("#kategori").focus();
                return false;
            }

            if(!keterangan.trim()) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Data Tidak Lengkap',
                    text: 'Silakan isi keterangan/keluhan pasien!'
                });
                $("#keterangan").focus();
                return false;
            }

            if(petugasChecked === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Data Tidak Lengkap',
                    text: 'Silakan pilih minimal satu petugas!'
                });
                return false;
            }

            // Konfirmasi sebelum submit dengan SweetAlert
            var confirmMsg = `Apakah Anda yakin ingin mengirim alert untuk:

Pasien: ${nama} (${norm})
Kategori: ${kategori}
Petugas: ${petugasChecked} orang

Alert akan dikirim melalui WhatsApp!`;

            Swal.fire({
                title: 'Konfirmasi Kirim Alert',
                text: confirmMsg,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Ya, Kirim Alert!',
                cancelButtonText: 'Batal'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Mengirim Alert...',
                        text: 'Sedang memproses dan mengirim WhatsApp',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Disable submit button
                    $("#submitBtn").prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Mengirim...');

                    // Submit form via AJAX
                    var formElement = this;
                    $.ajax({
                        url: $(formElement).attr('action'),
                        type: 'POST',
                        data: $(formElement).serialize(),
                        dataType: 'json',
                        success: function(response) {
                            console.log('AJAX Success:', response);
                            if(response.success) {
                                var icon = 'success';
                                if(response.type === 'warning') {
                                    icon = 'warning';
                                }

                                Swal.fire({
                                    icon: icon,
                                    title: 'Berhasil!',
                                    text: response.message,
                                    timer: 5000,
                                    showConfirmButton: true
                                }).then(() => {
                                    // Reset form
                                    $("#alertForm")[0].reset();
                                    $("#norm_hidden").val('');
                                    $("#nama_pasien").val('');
                                    $("#norm_input").val('');

                                    // Reload riwayat table
                                    if ($.fn.DataTable.isDataTable('#riwayatTable')) {
                                        $('#riwayatTable').DataTable().ajax.reload();
                                    }
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Gagal!',
                                    text: response.message
                                });
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX Error:', {xhr, status, error});
                            console.error('Response Text:', xhr.responseText);
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Terjadi kesalahan saat mengirim alert: ' + error
                            });
                        },
                        complete: function() {
                            // Re-enable submit button
                            $("#submitBtn").prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Kirim Alert Pasien');
                        }
                    });
                }
            });
        });

        // Initialize DataTable untuk riwayat
        if (typeof jQuery.fn.DataTable !== 'undefined') {
            $('#riwayatTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '<?= base_url("AlertPasien/get_riwayat_data") ?>',
                    type: 'POST'
                },
                columns: [
                    { data: 0, orderable: false }, // No
                    { data: 1 }, // Tanggal
                    { data: 2 }, // NORM
                    { data: 3 }, // Nama Pasien
                    { data: 4, orderable: false }, // Kategori
                    { data: 5, orderable: false }, // Keterangan
                    { data: 6, orderable: false } // Status
                ],
                pageLength: 10,
                lengthMenu: [10, 25, 50],
                order: [[1, 'desc']], // Order by tanggal desc
                language: {
                    processing: "Memuat data...",
                    search: "Cari:",
                    lengthMenu: "Tampilkan _MENU_ data per halaman",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(disaring dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    },
                    emptyTable: "Tidak ada data riwayat alert"
                }
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    });
}

// Start initialization when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAlertPasienForm);
} else {
    initAlertPasienForm();
}
</script>
