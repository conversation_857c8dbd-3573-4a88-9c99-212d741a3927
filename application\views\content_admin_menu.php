<style>
/* Fix modal z-index and backdrop issues */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
    .modal-backdrop.show {
        opacity: 0.5;
    }

    .modal-open {
        overflow: auto !important;
        padding-right: 0 !important;
    }
}

.modal-dialog {
    z-index: 1060 !important;
    margin: 1.75rem auto !important;
}

/* Responsive modal fixes */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 10px !important;
    }

    .modal-header {
        padding: 1rem !important;
    }

    .modal-body {
        padding: 1rem !important;
    }

    .modal-footer {
        padding: 1rem !important;
    }
}

/* Ensure modal is always visible */
.modal.show {
    display: block !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

/* Fix for overlapping elements */
.navbar,
.sidebar,
.fixed-top {
    z-index: 1040 !important;
}
</style>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">⚙️ Admin Menu</h2>
                    <p class="text-muted">Kelola hak akses menu dan data petugas</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-tabs" id="adminMenuTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-pane" type="button" role="tab" aria-controls="users-pane" aria-selected="true">
                        <i class="fas fa-users me-2"></i>
                        Daftar User dan Hak Akses Menu
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="petugas-tab" data-bs-toggle="tab" data-bs-target="#petugas-pane" type="button" role="tab" aria-controls="petugas-pane" aria-selected="false">
                        <i class="fas fa-user-tie me-2"></i>
                        Tabel Petugas
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if($this->session->flashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $this->session->flashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($this->session->flashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= $this->session->flashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Tab Content -->
    <div class="tab-content" id="adminMenuTabsContent">
        <!-- Tab 1: Users and Menu Access -->
        <div class="tab-pane fade show active" id="users-pane" role="tabpanel" aria-labelledby="users-tab">
            <!-- Statistics Cards -->
            <div class="row mb-4" id="userStatsCards">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total User</h6>
                                    <h3 class="mb-0" id="totalUsers">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">User Aktif</h6>
                                    <h3 class="mb-0" id="activeUsers">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">User Tanpa Akses</h6>
                                    <h3 class="mb-0" id="inactiveUsers">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-times fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>
                                Daftar User dan Hak Akses Menu
                            </h5>
                        </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">No</th>
                                    <th width="15%">Username</th>
                                    <th width="25%">Nama Lengkap</th>
                                    <th width="15%">Total Menu</th>
                                    <th width="20%">Status Akses</th>
                                    <th width="20%">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan dimuat via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div>

        <!-- Tab 2: Petugas Management -->
        <div class="tab-pane fade" id="petugas-pane" role="tabpanel" aria-labelledby="petugas-tab">
            <!-- Statistics Cards for Petugas -->
            <div class="row mb-4" id="petugasStatsCards">
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Total Petugas</h6>
                                    <h3 class="mb-0" id="totalPetugas">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-tie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Petugas Aktif</h6>
                                    <h3 class="mb-0" id="petugasAktif">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Petugas Nonaktif</h6>
                                    <h3 class="mb-0" id="petugasNonaktif">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-times fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Petugas Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-user-tie me-2"></i>
                                Tabel Petugas
                            </h5>
                            <button type="button" class="btn btn-light btn-sm" onclick="showAddPetugasModal()">
                                <i class="fas fa-plus me-1"></i>
                                Tambah Petugas
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="petugasTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="5%">No</th>
                                            <th width="15%">No. Absen</th>
                                            <th width="30%">Nama</th>
                                            <th width="20%">Nomor HP</th>
                                            <th width="15%">Status</th>
                                            <th width="15%">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Copy Access -->
<div class="modal fade" id="copyAccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Salin Hak Akses</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?= base_url('AdminMenu/copy_user_access') ?>" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="target_user_id" id="targetUserId">
                    
                    <div class="mb-3">
                        <label for="sourceUserId" class="form-label">Salin dari User:</label>
                        <select class="form-select" name="source_user_id" id="sourceUserId" required>
                            <option value="">Pilih User</option>
                            <?php foreach($users as $user): ?>
                                <?php if($user['total_menu_access'] > 0): ?>
                                    <option value="<?= $user['ID'] ?>">
                                        <?= $user['NAMA'] ?> (<?= $user['total_menu_access'] ?> menu)
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Hak akses akan disalin dari user yang dipilih ke user target. 
                        Hak akses lama akan diganti dengan yang baru.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-copy me-1"></i>
                        Salin Akses
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Hak Akses -->
<div class="modal fade" id="editAccessModal" tabindex="-1" aria-labelledby="editAccessModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAccessModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    Edit Hak Akses User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAccessForm">
                <div class="modal-body">
                    <!-- Loading State -->
                    <div id="loadingState" class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Memuat data...</p>
                    </div>

                    <!-- Content State -->
                    <div id="contentState" style="display: none;">
                        <input type="hidden" name="user_id" id="modalUserId">

                        <div class="mb-3 mt-5">
                            <h6><i class="fas fa-user me-2"></i>Informasi User</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <strong id="userName">-</strong><br>
                                    <small class="text-muted">Username: <span id="userLogin">-</span> | ID: <span id="userIdDisplay">-</span></small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6><i class="fas fa-list me-2"></i>Hak Akses Menu</h6>
                            <div class="row" id="menuCheckboxes">
                                <!-- Menu checkboxes akan dimuat di sini -->
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllMenus()">
                                    <i class="fas fa-check-double me-1"></i>Pilih Semua
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllMenus()">
                                    <i class="fas fa-times me-1"></i>Batal Semua
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div id="errorState" style="display: none;">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorMessage">Terjadi kesalahan saat memuat data</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="saveButton">
                        <i class="fas fa-save me-1"></i>
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Tambah Petugas -->
<div class="modal fade" id="addPetugasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Petugas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPetugasForm" onsubmit="event.preventDefault(); savePetugas();">
                <div class="modal-body">
                    <div class="mb-3 mt-4">
                        <label for="no_absen" class="form-label">No. Absen <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="no_absen" name="no_absen" required>
                            <button type="button" class="btn btn-outline-primary" onclick="searchPegawaiByAbsen()">
                                <i class="fas fa-search"></i> Cari
                            </button>
                        </div>
                        <small class="form-text text-muted">Masukkan No Absen dan klik Cari untuk mengisi data otomatis</small>
                    </div>
                    <div class="mb-3">
                        <label for="nama" class="form-label">Nama <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nama" name="nama" required>
                    </div>
                    <div class="mb-3">
                        <label for="nomor_hp" class="form-label">Nomor HP <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nomor_hp" name="nomor_hp" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Petugas -->
<div class="modal fade" id="editPetugasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Petugas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPetugasForm" onsubmit="event.preventDefault(); updatePetugas();">
                <div class="modal-body">
                    <input type="hidden" id="editPetugasId" name="id">
                    <div class="mb-3 mt-5">
                        <label for="editNoAbsen" class="form-label">No. Absen <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editNoAbsen" name="no_absen" required>
                    </div>
                    <div class="mb-3">
                        <label for="editNama" class="form-label">Nama <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editNama" name="nama" required>
                    </div>
                    <div class="mb-3">
                        <label for="editNomorHp" class="form-label">Nomor HP <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editNomorHp" name="nomor_hp" required>
                    </div>
                    <div class="mb-3">
                        <label for="editStatus" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="editStatus" name="status" required>
                            <option value="1">Aktif</option>
                            <option value="0">Nonaktif</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Ensure jQuery is available globally first
if (typeof jQuery !== 'undefined') {
    window.$ = window.jQuery = jQuery;
}

// Wait for all libraries to be loaded
function initAdminMenu() {
    // Check if jQuery is available
    if (typeof jQuery === 'undefined' || typeof $ === 'undefined') {
        console.log('jQuery not loaded yet, retrying...');
        setTimeout(initAdminMenu, 100);
        return;
    }

    // Check if DataTables is available
    if (typeof jQuery.fn.DataTable === 'undefined') {
        console.log('DataTables not loaded yet, retrying...');
        setTimeout(initAdminMenu, 100);
        return;
    }

    // Ensure $ is available globally
    window.$ = window.jQuery = jQuery;

    $(document).ready(function() {
        try {
            console.log('AdminMenu: All libraries loaded, jQuery version:', $.fn.jquery);
            console.log('DataTables version:', $.fn.DataTable.version);

        // Load user statistics
        loadUserStats();

        // Setup form submit event handler
        console.log('Setting up form submit event handler...');
        setupFormSubmitHandler();

        // Initialize petugas tab if it's the active tab
        setTimeout(function() {
            if ($('#petugas-tab').hasClass('active') || $('#petugas-pane').hasClass('show active')) {
                console.log('Petugas tab is active, initializing...');
                initPetugasTab();
            } else {
                console.log('Petugas tab is not active');
            }
        }, 200);

        // Add tab change event handler
        $('#adminMenuTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            try {
                var target = $(e.target).attr("data-bs-target");
                console.log('Tab changed to:', target);

                if (target === '#petugas-pane') {
                    console.log('Petugas tab activated, initializing...');
                    setTimeout(function() {
                        initPetugasTab();
                    }, 100);
                }
            } catch (error) {
                console.error('Error in tab change handler:', error);
            }
        });

        // Also initialize petugas tab immediately for testing
        console.log('Force initializing petugas tab for testing...');
        setTimeout(function() {
            try {
                initPetugasTab();
            } catch (error) {
                console.error('Error in force petugas tab init:', error);
            }
        }, 500);

        // Debug: Test petugas endpoints
        console.log('Testing petugas endpoints...');
        testPetugasEndpoints();

        // Test if SweetAlert is available
        if (typeof Swal !== 'undefined') {
            console.log('SweetAlert2 is available');
        } else {
            console.error('SweetAlert2 is NOT available');
        }

        // Initialize DataTable with serverside processing
        var table = $('#usersTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?= base_url("AdminMenu/get_users_data") ?>',
                type: 'POST'
            },
            columns: [
                { data: 0, orderable: false, responsivePriority: 1 }, // No
                { data: 1, responsivePriority: 2 }, // Username
                { data: 2, responsivePriority: 3 }, // Nama
                { data: 3, orderable: false, responsivePriority: 5 }, // Total Menu
                { data: 4, orderable: false, responsivePriority: 6 }, // Status
                { data: 5, orderable: false, responsivePriority: 1 } // Aksi - Priority tinggi agar selalu terlihat
            ],
            pageLength: 10,
            lengthMenu: [10, 25, 50, 100],
            responsive: {
                details: {
                    type: 'column',
                    target: 'tr'
                }
            },
            language: {
                processing: "Memuat data...",
                search: "Cari:",
                lengthMenu: "Tampilkan _MENU_ data per halaman",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                infoFiltered: "(difilter dari _MAX_ total data)",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya"
                },
                emptyTable: "Tidak ada data petugas"
            },
            order: [[1, 'asc']] // Sort by username
        });

        // Add event listener for modal events
        $('#editAccessModal').on('shown.bs.modal', function () {
            console.log('Edit Access Modal shown');
        });

        $('#editAccessModal').on('hidden.bs.modal', function () {
            console.log('Edit Access Modal hidden');
            // Reset modal to loading state when closed
            showModalState('loading');
        });

        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);

        console.log('AdminMenu initialized successfully');

        } catch (error) {
            console.error('Error initializing AdminMenu:', error);
        }
    });
}

// Debug function to test petugas endpoints manually
window.testPetugasManually = function() {
    console.log('=== Manual Petugas Test ===');

    // Test get_petugas_stats
    $.ajax({
        url: '<?= base_url("AdminMenu/get_petugas_stats") ?>',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log('✓ get_petugas_stats SUCCESS:', response);
        },
        error: function(xhr, status, error) {
            console.error('✗ get_petugas_stats ERROR:', status, error);
            console.error('Response:', xhr.responseText);
        }
    });

    // Test get_petugas_data
    $.ajax({
        url: '<?= base_url("AdminMenu/get_petugas_data") ?>',
        type: 'POST',
        dataType: 'json',
        data: {
            draw: 1,
            start: 0,
            length: 10,
            search: { value: '', regex: false },
            order: [{ column: 0, dir: 'asc' }]
        },
        success: function(response) {
            console.log('✓ get_petugas_data SUCCESS:', response);
        },
        error: function(xhr, status, error) {
            console.error('✗ get_petugas_data ERROR:', status, error);
            console.error('Response:', xhr.responseText);
        }
    });
};

// Start initialization when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initAdminMenu);
} else {
    initAdminMenu();
}

// Function untuk load user statistics
function loadUserStats() {
    $.ajax({
        url: '<?= base_url("AdminMenu/get_user_stats") ?>',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            $('#totalUsers').text(data.total_users);
            $('#activeUsers').text(data.active_users);
            $('#inactiveUsers').text(data.inactive_users);
        },
        error: function() {
            $('#totalUsers').text('Error');
            $('#activeUsers').text('Error');
            $('#inactiveUsers').text('Error');
        }
    });
}

// Function untuk edit user access
function editUserAccess(userId) {
    console.log('editUserAccess called with userId:', userId);

    // Show modal
    $('#editAccessModal').modal('show');
    console.log('Modal show called');

    // Show loading state
    showModalState('loading');

    // Load user data via AJAX
    console.log('Making AJAX call to get user menu data...');
    $.ajax({
        url: '<?= base_url("AdminMenu/get_user_menu_data") ?>',
        type: 'POST',
        data: { user_id: userId },
        dataType: 'json',
        success: function(response) {
            console.log('AJAX response received:', response);
            if (response.success) {
                // Populate user info
                $('#modalUserId').val(response.user.ID);
                $('#userName').text(response.user.NAMA);
                $('#userLogin').text(response.user.LOGIN);
                $('#userIdDisplay').text(response.user.ID);

                // Clear and populate menu checkboxes
                var menuContainer = $('#menuCheckboxes');
                menuContainer.empty();

                response.menus.forEach(function(menu) {
                    var checked = menu.has_access == 1 ? 'checked' : '';
                    var menuHtml = `
                        <div class="col-md-6 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="menu_ids[]" value="${menu.ID}" ${checked} id="menu_${menu.ID}">
                                <label class="form-check-label" for="menu_${menu.ID}">
                                    <i class="fas ${menu.ICON} me-2"></i>${menu.LABEL}
                                </label>
                            </div>
                        </div>
                    `;
                    menuContainer.append(menuHtml);
                });

                // Show content state
                showModalState('content');

                console.log('Form content loaded successfully');
            } else {
                showModalState('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', {xhr, status, error});
            console.error('Response Text:', xhr.responseText);
            showModalState('error', 'Terjadi kesalahan saat memuat data: ' + error);
        }
    });
}

// Function untuk mengatur state modal
function showModalState(state, message = '') {
    // Hide all states
    $('#loadingState, #contentState, #errorState').hide();

    switch(state) {
        case 'loading':
            $('#loadingState').show();
            $('#saveButton').prop('disabled', true);
            break;
        case 'content':
            $('#contentState').show();
            $('#saveButton').prop('disabled', false);
            break;
        case 'error':
            $('#errorState').show();
            $('#errorMessage').text(message);
            $('#saveButton').prop('disabled', true);
            break;
    }
}

// Function untuk setup form submit handler
function setupFormSubmitHandler() {
    // Remove any existing handlers to prevent duplicates
    $(document).off('submit', '#editAccessForm');

    // Setup new handler with event delegation
    $(document).on('submit', '#editAccessForm', function(e) {
        e.preventDefault();
        console.log('Form submit intercepted! Event handler is working.');
        console.log('Form element:', this);
        console.log('Form jQuery object:', $(this));

        var formData = $(this).serialize();
        console.log('Form data:', formData);

        // Test SweetAlert first
        if (typeof Swal === 'undefined') {
            alert('SweetAlert2 not loaded!');
            return false;
        }

        // Validate that at least user_id is present
        var userId = $(this).find('input[name="user_id"]').val();
        if (!userId) {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'User ID tidak ditemukan. Silakan tutup modal dan coba lagi.'
            });
            return false;
        }

        // Show loading
        Swal.fire({
            title: 'Menyimpan...',
            text: 'Sedang memperbarui hak akses user',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: '<?= base_url("AdminMenu/update_user_access") ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('AJAX Success:', response);
                $('#editAccessModal').modal('hide');

                // Reload DataTable if it exists
                if ($.fn.DataTable.isDataTable('#usersTable')) {
                    $('#usersTable').DataTable().ajax.reload();
                }

                if(response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: response.message,
                        timer: 3000,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Gagal!',
                        text: response.message
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', {xhr, status, error});
                console.error('Response Text:', xhr.responseText);

                var errorMessage = 'Terjadi kesalahan saat menyimpan data';
                if (xhr.responseText) {
                    try {
                        var errorResponse = JSON.parse(xhr.responseText);
                        if (errorResponse.message) {
                            errorMessage = errorResponse.message;
                        }
                    } catch (e) {
                        errorMessage += ': ' + xhr.responseText.substring(0, 100);
                    }
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: errorMessage
                });
            }
        });
    });

    console.log('Form submit handler setup completed');
}

// Function untuk select all menus
function selectAllMenus() {
    $('#editAccessModal input[name="menu_ids[]"]').prop('checked', true);
}

// Function untuk deselect all menus
function deselectAllMenus() {
    $('#editAccessModal input[name="menu_ids[]"]').prop('checked', false);
}



// Function untuk copy access
function copyAccess(userId) {
    $('#targetUserId').val(userId);
    $('#copyAccessModal').modal('show');
}

// Function untuk reset access
function resetAccess(userId) {
    Swal.fire({
        title: 'Konfirmasi Reset',
        text: 'Apakah Anda yakin ingin menghapus semua hak akses user ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Reset!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Mereset...',
                text: 'Sedang menghapus hak akses user',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '<?= base_url("AdminMenu/reset_user_access/") ?>' + userId,
                type: 'GET',
                success: function(response) {
                    $('#usersTable').DataTable().ajax.reload();

                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: 'Hak akses user berhasil direset.',
                        timer: 3000,
                        showConfirmButton: false
                    });
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mereset hak akses.'
                    });
                }
            });
        }
    });
}

// ==================== PETUGAS MANAGEMENT ====================

// Test Petugas Endpoints
function testPetugasEndpoints() {
    // Test endpoints silently
    $.ajax({
        url: '<?= base_url("AdminMenu/get_petugas_stats") ?>',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            $('#totalPetugas').text(response.total || '0');
            $('#petugasAktif').text(response.aktif || '0');
            $('#petugasNonaktif').text(response.nonaktif || '0');
        }
    });
}

// Initialize Petugas Tab
function initPetugasTab() {
    try {
        console.log('initPetugasTab() called');

        // Always load petugas statistics regardless of tab visibility
        console.log('Loading petugas statistics...');
        
        // Load petugas statistics
        try {
            loadPetugasStats();
        } catch (error) {
            console.error('Error loading petugas stats:', error);
        }

        // Initialize Petugas DataTable
        try {
            if ($.fn.DataTable.isDataTable('#petugasTable')) {
                console.log('Destroying existing DataTable');
                $('#petugasTable').DataTable().destroy();
            }
        } catch (error) {
            console.error('Error destroying existing DataTable:', error);
        }
        
        console.log('Initializing new DataTable for petugas');

        try {
            var table = $('#petugasTable').DataTable({
                processing: true,
                serverSide: true,
                destroy: true,
                ajax: {
                    url: '<?= base_url("AdminMenu/get_petugas_data") ?>',
                    type: 'POST',
                    error: function(xhr, error, thrown) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error Loading Data',
                            text: 'Gagal memuat data petugas. Silakan refresh halaman.',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                columns: [
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    { data: 'no_absen' },
                    { data: 'nama' },
                    { data: 'nomor_hp' },
                    {
                        data: 'status',
                        orderable: false,
                        render: function(data, type, row) {
                            if (data == 'aktif') {
                                return '<span class="badge bg-success">Aktif</span>';
                            } else {
                                return '<span class="badge bg-danger">Nonaktif</span>';
                            }
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return '<div class="btn-group" role="group">' +
                                   '<button type="button" class="btn btn-sm btn-primary" onclick="editPetugas(' + row.id + ')" title="Edit">' +
                                   '<i class="fas fa-edit"></i></button>' +
                                   '<button type="button" class="btn btn-sm btn-warning" onclick="toggleStatusPetugas(' + row.id + ', \'' + (row.status == 'aktif' ? 'nonaktif' : 'aktif') + '\')" title="Toggle Status">' +
                                   '<i class="fas fa-' + (row.status == 'aktif' ? 'ban' : 'check') + '"></i></button>' +
                                   '</div>';
                        }
                    }
                ],
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                language: {
                    processing: "Memuat data...",
                    search: "Cari:",
                    lengthMenu: "Tampilkan _MENU_ data per halaman",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(difilter dari _MAX_ total data)",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    },
                    emptyTable: "Tidak ada data petugas"
                }
            });
            
            console.log('Petugas DataTable initialized successfully');
        } catch (error) {
            console.error('Error initializing DataTable:', error);
        }
    } catch (error) {
        console.error('Error in initPetugasTab:', error);
    }
}

// Load Petugas Statistics
function loadPetugasStats() {
    try {
        $.ajax({
            url: '<?= base_url("AdminMenu/get_petugas_stats") ?>',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                try {
                    $('#totalPetugas').text(response.total || '-');
                    $('#petugasAktif').text(response.aktif || '-');
                    $('#petugasNonaktif').text(response.nonaktif || '-');
                } catch (error) {
                    console.error('Error updating petugas stats display:', error);
                    $('#totalPetugas').text('-');
                    $('#petugasAktif').text('-');
                    $('#petugasNonaktif').text('-');
                }
            },
            error: function(xhr, error, thrown) {
                console.error('Error loading petugas stats:', error, thrown);
                $('#totalPetugas').text('-');
                $('#petugasAktif').text('-');
                $('#petugasNonaktif').text('-');
            }
        });
    } catch (error) {
        console.error('Error in loadPetugasStats:', error);
        $('#totalPetugas').text('-');
        $('#petugasAktif').text('-');
        $('#petugasNonaktif').text('-');
    }
}

// Show Add Petugas Modal
function showAddPetugasModal() {
    $('#addPetugasModal').modal('show');
    $('#addPetugasForm')[0].reset();
    $('#nama, #nomor_hp').prop('readonly', false);
}

// Search Pegawai by No Absen
function searchPegawaiByAbsen() {
    var noAbsen = $('#no_absen').val().trim();

    if (!noAbsen) {
        Swal.fire({
            icon: 'warning',
            title: 'Peringatan',
            text: 'Silakan masukkan No Absen terlebih dahulu.'
        });
        return;
    }

    // Show loading
    Swal.fire({
        title: 'Mencari...',
        text: 'Sedang mencari data pegawai',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '<?= base_url("AdminMenu/search_pegawai_by_absen") ?>',
        type: 'POST',
        data: { no_absen: noAbsen },
        dataType: 'json',
        success: function(response) {
            Swal.close();

            if (response.success) {
                $('#nama').val(response.data.NAMA_LENGKAP).prop('readonly', true);
                $('#nomor_hp').val(response.data.NOMOR_HP).prop('readonly', true);

                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: 'Data pegawai ditemukan.',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                $('#nama, #nomor_hp').val('').prop('readonly', false);
                Swal.fire({
                    icon: 'error',
                    title: 'Tidak Ditemukan',
                    text: response.message
                });
            }
        },
        error: function() {
            Swal.close();
            $('#nama, #nomor_hp').val('').prop('readonly', false);
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Terjadi kesalahan saat mencari data pegawai.'
            });
        }
    });
}

// Save Petugas
function savePetugas() {
    var formData = $('#addPetugasForm').serialize();

    // Show loading
    Swal.fire({
        title: 'Menyimpan...',
        text: 'Sedang menambahkan petugas',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '<?= base_url("AdminMenu/save_petugas") ?>',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            $('#addPetugasModal').modal('hide');

            if (response.success) {
                $('#petugasTable').DataTable().ajax.reload();
                loadPetugasStats();

                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: response.message,
                    timer: 3000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: response.message
                });
            }
        },
        error: function() {
            $('#addPetugasModal').modal('hide');
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Terjadi kesalahan saat menyimpan data petugas.'
            });
        }
    });
}

// Edit Petugas
function editPetugas(id) {
    $.ajax({
        url: '<?= base_url("AdminMenu/get_petugas_data_edit") ?>',
        type: 'POST',
        data: { id: id },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#editPetugasId').val(response.data.ID);
                $('#editNoAbsen').val(response.data.NO_ABSEN);
                $('#editNama').val(response.data.NAMA);
                $('#editNomorHp').val(response.data.NOMOR_HP);
                $('#editStatus').val(response.data.STATUS);

                $('#editPetugasModal').modal('show');
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: response.message
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Terjadi kesalahan saat mengambil data petugas.'
            });
        }
    });
}

// Update Petugas
function updatePetugas() {
    var formData = $('#editPetugasForm').serialize();

    // Show loading
    Swal.fire({
        title: 'Menyimpan...',
        text: 'Sedang memperbarui data petugas',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.ajax({
        url: '<?= base_url("AdminMenu/update_petugas") ?>',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            $('#editPetugasModal').modal('hide');

            if (response.success) {
                $('#petugasTable').DataTable().ajax.reload();
                loadPetugasStats();

                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil!',
                    text: response.message,
                    timer: 3000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Gagal!',
                    text: response.message
                });
            }
        },
        error: function() {
            $('#editPetugasModal').modal('hide');
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'Terjadi kesalahan saat memperbarui data petugas.'
            });
        }
    });
}

// Toggle Status Petugas
function toggleStatusPetugas(id, status) {
    var statusText = status == 1 ? 'mengaktifkan' : 'menonaktifkan';

    Swal.fire({
        title: 'Konfirmasi',
        text: 'Apakah Anda yakin ingin ' + statusText + ' petugas ini?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya, ' + (status == 1 ? 'Aktifkan' : 'Nonaktifkan') + '!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?= base_url("AdminMenu/toggle_status_petugas") ?>',
                type: 'POST',
                data: { id: id, status: status },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#petugasTable').DataTable().ajax.reload();
                        loadPetugasStats();

                        Swal.fire({
                            icon: 'success',
                            title: 'Berhasil!',
                            text: response.message,
                            timer: 3000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            text: response.message
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengubah status petugas.'
                    });
                }
            });
        }
    });
}

// Duplicate tab change event handlers removed - handled in main initialization section

// Duplicate page load handler removed - handled in main initialization section

// Fix modal backdrop issues
$(document).on('show.bs.modal', '.modal', function() {
    var zIndex = 1055;
    $(this).css('z-index', zIndex);
    setTimeout(function() {
        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
    }, 0);
});

$(document).on('hidden.bs.modal', '.modal', function() {
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open').css('padding-right', '');
});

// Ensure modals work on mobile
$(document).on('shown.bs.modal', '.modal', function() {
    $('body').addClass('modal-open');
    if ($(window).width() < 768) {
        $(this).css({
            'margin-top': '10px',
            'margin-bottom': '10px'
        });
    }

    // Prevent multiple backdrop
    if ($('.modal-backdrop').length > 1) {
        $('.modal-backdrop').not(':last').remove();
    }
});

// Global initNavbarToggle function - moved to template_dashboard.php
// This section is no longer needed as the function is now defined globally
</script>
