<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AlertPasien_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Cari pasien berdasarkan NORM atau nama untuk autocomplete
     */
    public function get_pasien_autocomplete($search_term) {
        $this->db->select('NORM, NAMA');
        $this->db->from('master.pasien');
        $this->db->group_start();
            $this->db->like('NORM', $search_term);
            $this->db->or_like('NAMA', $search_term);
        $this->db->group_end();
        $this->db->limit(10);
        $this->db->order_by('NORM', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil data pasien berdasarkan NORM
     */
    public function get_pasien_by_norm($norm) {
        $this->db->select('NORM, NAMA');
        $this->db->from('master.pasien');
        $this->db->where('NORM', $norm);
        
        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Ambil semua petugas WhatsApp
     */
    public function get_all_petugas() {
        $this->db->select('*');
        $this->db->from('aplikasi.towerc_petugas');
        $this->db->where('STATUS', 1);
        $this->db->order_by('NAMA', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Simpan alert pasien baru
     */
    public function save_alert($data) {
        $alert_data = array(
            'NORM' => $data['norm'],
            'NAMA_PASIEN' => $data['nama_pasien'],
            'KATEGORI' => $data['kategori'],
            'KETERANGAN' => $data['keterangan'],
            'CREATED_BY' => $this->session->userdata('id'),
            'STATUS' => 1
        );
        
        $this->db->insert('log.towerc_alert_pasien', $alert_data);
        return $this->db->insert_id();
    }

    /**
     * Simpan log pengiriman WhatsApp
     */
    public function save_whatsapp_log($alert_id, $petugas_id, $nomor_hp, $pesan, $status = 'pending', $response = null) {
        $log_data = array(
            'ALERT_ID' => $alert_id,
            'PETUGAS_ID' => $petugas_id,
            'NOMOR_HP' => $nomor_hp,
            'PESAN' => is_array($pesan) ? json_encode($pesan) : $pesan,
            'STATUS' => $status,
            'RESPONSE' => is_array($response) ? json_encode($response) : $response
        );
        
        $this->db->insert('log.towerc_whatsapp_log', $log_data);
        $whatsapp_log_id = $this->db->insert_id();
        
        // Simpan detail petugas
        if ($whatsapp_log_id) {
            $this->save_whatsapp_log_detail($alert_id, $whatsapp_log_id, $petugas_id, $nomor_hp, $status, $response);
        }
        
        return $whatsapp_log_id;
    }

    /**
     * Simpan detail log WhatsApp dengan info petugas
     */
    public function save_whatsapp_log_detail($alert_id, $whatsapp_log_id, $petugas_id, $nomor_hp, $status, $response) {
        // Ambil data petugas
        $petugas = $this->get_petugas_by_id($petugas_id);
        $nama_petugas = $petugas ? $petugas['NAMA'] : 'Unknown';
        
        // Extract MSG_ID dari response jika ada
        $msg_id = null;
        if (is_array($response) && isset($response['data']) && isset($response['data']['data'])) {
            $data_array = $response['data']['data'];
            if (is_array($data_array) && count($data_array) > 0 && isset($data_array[0]['msgId'])) {
                $msg_id = $data_array[0]['msgId'];
            }
        }
        
        $detail_data = array(
            'ALERT_ID' => $alert_id,
            'WHATSAPP_LOG_ID' => $whatsapp_log_id,
            'PETUGAS_ID' => $petugas_id,
            'NAMA_PETUGAS' => $nama_petugas,
            'NOMOR_HP' => $nomor_hp,
            'STATUS_KIRIM' => $status,
            'MSG_ID' => $msg_id,
            'RESPONSE_DATA' => is_array($response) ? json_encode($response) : $response
        );
        
        $this->db->insert('log.towerc_whatsapp_log_detail', $detail_data);
        return $this->db->insert_id();
    }

    /**
     * Update status log WhatsApp
     */
    public function update_whatsapp_log_status($log_id, $status, $response = null) {
        $update_data = array(
            'STATUS' => $status,
            'RESPONSE' => $response
        );
        
        $this->db->where('ID', $log_id);
        return $this->db->update('log.towerc_whatsapp_log', $update_data);
    }

    /**
     * Ambil riwayat alert pasien
     */
    public function get_alert_history($limit = 50, $offset = 0) {
        $this->db->select('
            ap.*,
            u.NAMA as CREATED_BY_NAME
        ');
        $this->db->from('log.towerc_alert_pasien ap');
        $this->db->join('aplikasi.pengguna u', 'u.ID = ap.CREATED_BY', 'left');
        $this->db->where('ap.STATUS', 1);
        $this->db->order_by('ap.CREATED_AT', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Ambil detail alert dengan log WhatsApp
     */
    public function get_alert_detail($alert_id) {
        // Ambil data alert
        $this->db->select('
            ap.*,
            u.NAMA as CREATED_BY_NAME
        ');
        $this->db->from('log.towerc_alert_pasien ap');
        $this->db->join('aplikasi.pengguna u', 'u.ID = ap.CREATED_BY', 'left');
        $this->db->where('ap.ID', $alert_id);
        
        $alert = $this->db->get()->row_array();
        
        if ($alert) {
            // Ambil log WhatsApp untuk alert ini
            $this->db->select('
                wl.*,
                p.NAMA as PETUGAS_NAMA
            ');
            $this->db->from('log.towerc_whatsapp_log wl');
            $this->db->join('aplikasi.towerc_petugas p', 'p.ID = wl.PETUGAS_ID', 'left');
            $this->db->where('wl.ALERT_ID', $alert_id);
            $this->db->order_by('wl.CREATED_AT', 'ASC');
            
            $whatsapp_logs = $this->db->get()->result_array();
            $alert['whatsapp_logs'] = $whatsapp_logs;
        }
        
        return $alert;
    }

    /**
     * Ambil statistik alert
     */
    public function get_alert_statistics() {
        $this->db->select('KATEGORI, COUNT(*) as total');
        $this->db->from('log.towerc_alert_pasien');
        $this->db->where('STATUS', 1);
        $this->db->where('DATE(CREATED_AT)', date('Y-m-d')); // Hari ini
        $this->db->group_by('KATEGORI');
        
        $query = $this->db->get();
        $result = $query->result_array();
        
        // Format hasil untuk kemudahan penggunaan
        $stats = array(
            'kategori' => array(
                'High' => 0,
                'Medium' => 0,
                'Low' => 0
            ),
            'total' => 0
        );
        
        foreach ($result as $row) {
            $stats['kategori'][$row['KATEGORI']] = (int)$row['total'];
            $stats['total'] += (int)$row['total'];
        }
        
        return $stats;
    }

    /**
     * Cek apakah pasien sudah pernah di-alert hari ini
     */
    public function check_duplicate_alert_today($norm) {
        $this->db->select('COUNT(*) as count');
        $this->db->from('log.towerc_alert_pasien');
        $this->db->where('NORM', $norm);
        $this->db->where('DATE(CREATED_AT)', date('Y-m-d'));
        $this->db->where('STATUS', 1);
        
        $query = $this->db->get();
        $result = $query->row_array();
        
        return (int)$result['count'] > 0;
    }

    /**
     * Ambil petugas berdasarkan ID
     */
    public function get_petugas_by_id($petugas_id) {
        $this->db->select('*');
        $this->db->from('aplikasi.towerc_petugas');
        $this->db->where('ID', $petugas_id);
        $this->db->where('STATUS', 1);

        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Get riwayat data untuk DataTables
     */
    public function get_riwayat_datatables($start, $length, $search_value, $order_column, $order_dir) {
        $this->db->select('ID as id, CREATED_AT as created_at, NORM as norm, NAMA_PASIEN as nama_pasien, KATEGORI as kategori, KETERANGAN as keterangan, STATUS as status');
        $this->db->from('log.towerc_alert_pasien');

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('NORM', $search_value);
            $this->db->or_like('NAMA_PASIEN', $search_value);
            $this->db->or_like('KATEGORI', $search_value);
            $this->db->or_like('KETERANGAN', $search_value);
            $this->db->group_end();
        }

        if (!empty($order_column) && !empty($order_dir)) {
            // Map lowercase column names to uppercase database fields
            $column_map = array(
                'id' => 'ID',
                'created_at' => 'CREATED_AT',
                'norm' => 'NORM',
                'nama_pasien' => 'NAMA_PASIEN',
                'kategori' => 'KATEGORI',
                'keterangan' => 'KETERANGAN',
                'status' => 'STATUS'
            );
            $db_column = isset($column_map[$order_column]) ? $column_map[$order_column] : 'CREATED_AT';
            $this->db->order_by($db_column, $order_dir);
        } else {
            $this->db->order_by('CREATED_AT', 'DESC');
        }

        $this->db->limit($length, $start);

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Count total riwayat
     */
    public function count_all_riwayat() {
        $this->db->from('log.towerc_alert_pasien');
        return $this->db->count_all_results();
    }

    /**
     * Count filtered riwayat
     */
    public function count_filtered_riwayat($search_value) {
        $this->db->from('log.towerc_alert_pasien');

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('NORM', $search_value);
            $this->db->or_like('NAMA_PASIEN', $search_value);
            $this->db->or_like('KATEGORI', $search_value);
            $this->db->or_like('KETERANGAN', $search_value);
            $this->db->group_end();
        }

        return $this->db->count_all_results();
    }

    /**
     * Get WhatsApp logs by alert ID
     */
    public function get_whatsapp_logs_by_alert($alert_id) {
        $this->db->select('*');
        $this->db->from('log.towerc_whatsapp_log');
        $this->db->where('ALERT_ID', $alert_id); // Use uppercase field name

        $query = $this->db->get();
        return $query->result_array();
    }
}
