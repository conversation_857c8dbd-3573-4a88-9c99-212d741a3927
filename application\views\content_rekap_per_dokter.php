

<!-- Page Title Section -->
<div class="container-fluid py-3">
    <div class="row">
        <div class="col-md-6">
            <h3 class="mb-0">REKAP KETEPATAN DPJP PRAKTEK PER DOKTER</h3>
        </div>
        <div class="col-md-3 text-center">
            <div id="tanggalku" style="font-size: 24px; color: #2c3e50; font-weight: bold;"></div>
        </div>
        <div class="col-md-3 text-end">
            <h5 class="mb-1"><?= date("l, d F Y") ?></h5>
            <h6 class="total_dokter text-muted"><b>Total Dokter ()</b></h6>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container-fluid pb-4">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card summary-card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Total Dokter</h4>
                    <h2 id="total_dokter_count">0</h2>
                    <small><i class="fas fa-user-md"></i> Dokter Aktif</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card bg-info text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Lebih Awal</h4>
                    <h2 id="total_lebih_awal_count">0</h2>
                    <small><i class="fas fa-fast-forward"></i> Total Pasien</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card bg-success text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Tepat Waktu</h4>
                    <h2 id="total_tepat_waktu_count">0</h2>
                    <small><i class="fas fa-check-circle"></i> Total Pasien</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card summary-card bg-danger text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Terlambat</h4>
                    <h2 id="total_terlambat_count">0</h2>
                    <small><i class="fas fa-clock"></i> Total Pasien</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Summary Row -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Total Praktek</h4>
                    <h2 id="total_pasien_count">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4 class="card-title">Rata-rata % Tepat Waktu + Lebih Awal</h4>
                    <h2 id="rata_rata_persentase">0%</h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filter Section -->
    <div class="row">
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Tanggal Awal</label>
                                <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d\T00:00') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Tanggal Akhir</label>
                                <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d\T23:59') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" id="btnFilter" class="btn btn-primary form-control">
                                    <i class="fas fa-search"></i> Filter Data
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Data Table -->
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="rekapDokterTable" class="display" style="width:100%">
                            <thead>
                                <tr style="text-align:center">
                                    <th>No</th>
                                    <th>Nama Dokter</th>
                                    <th>Total Praktek</th>
                                    <th>Lebih Awal</th>
                                    <th>Tepat Waktu</th>
                                    <th>Terlambat</th>
                                    <th>% Tepat Waktu + Lebih Awal</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Wait for template scripts to load
function initRekapDokterTable() {
    // Check if jQuery and DataTables are available
    if (typeof jQuery === 'undefined') {
        console.log('jQuery not loaded yet, retrying...');
        setTimeout(initRekapDokterTable, 100);
        return;
    }
    
    if (typeof jQuery.fn.DataTable === 'undefined') {
        console.log('DataTables not loaded yet, retrying...');
        setTimeout(initRekapDokterTable, 100);
        return;
    }
    
    $(document).ready(function() {
        console.log('Initializing Rekap Dokter DataTable...');

        var table = $('#rekapDokterTable').DataTable({
            responsive: true,
            processing: true,
            serverSide: false,
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'pdfHtml5',
                    text: '<i class="fas fa-file-pdf"></i> Export PDF',
                    className: 'btn btn-danger btn-sm',
                    title: function() {
                        var tglAwal = $('#tgl_awal').val();
                        var tglAkhir = $('#tgl_akhir').val();
                        var periode = '';
                        
                        if (tglAwal && tglAkhir) {
                            var startDate = new Date(tglAwal).toLocaleDateString('id-ID');
                            var endDate = new Date(tglAkhir).toLocaleDateString('id-ID');
                            periode = 'Periode: ' + startDate + ' - ' + endDate;
                        } else {
                            periode = 'Periode: ' + new Date().toLocaleDateString('id-ID');
                        }
                        
                        return 'Rekap Per Dokter - ' + periode;
                    },
                    orientation: 'landscape',
                    pageSize: 'A4',
                    exportOptions: {
                        columns: ':visible'
                    },
                    customize: function(doc) {
                        doc.defaultStyle.fontSize = 8;
                        doc.styles.tableHeader.fontSize = 9;
                        doc.styles.tableHeader.fillColor = '#2c3e50';
                    }
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fas fa-file-excel"></i> Export Excel',
                    className: 'btn btn-success btn-sm',
                    title: function() {
                        var tglAwal = $('#tgl_awal').val();
                        var tglAkhir = $('#tgl_akhir').val();
                        var periode = '';
                        
                        if (tglAwal && tglAkhir) {
                            var startDate = new Date(tglAwal).toLocaleDateString('id-ID');
                            var endDate = new Date(tglAkhir).toLocaleDateString('id-ID');
                            periode = 'Periode: ' + startDate + ' - ' + endDate;
                        } else {
                            periode = 'Periode: ' + new Date().toLocaleDateString('id-ID');
                        }
                        
                        return 'Rekap Per Dokter - ' + periode;
                    },
                    exportOptions: {
                        columns: ':visible'
                    }
                }
            ],
            ajax: {
                url: 'RekapPerDokter/get_rekap_data',
                type: 'POST',
                data: function(d) {
                    d.tgl_awal = $('#tgl_awal').val();
                    d.tgl_akhir = $('#tgl_akhir').val();
                    console.log('Sending data:', d);
                },
                dataSrc: function(json) {
                    console.log('DataTable response:', json);
                    if (json.error) {
                        console.error('Server error:', json.error);
                        alert('Error: ' + json.error);
                        return [];
                    }
                    
                    // Update total count and summary
                    if (json.recordsTotal !== undefined) {
                        $(".total_dokter").text('Total Dokter (' + json.recordsTotal + ')');
                        loadSummaryData();
                    }
                    
                    return json.data || [];
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    console.error('Response:', xhr.responseText);
                    alert('AJAX Error: ' + error + '\nCheck console for details');
                }
            },
            columnDefs: [
                {
                    targets: [0, 2, 3, 4, 5], // Center align numeric columns
                    className: 'text-center'
                },
                {
                    targets: [6], // Percentage column
                    render: function(data, type, row) {
                        var percentage = parseFloat(data);
                        var badgeClass = 'badge-poor';
                        
                        if (percentage >= 90) {
                            badgeClass = 'badge-excellent';
                        } else if (percentage >= 80) {
                            badgeClass = 'badge-good';
                        } else if (percentage >= 70) {
                            badgeClass = 'badge-average';
                        }
                        
                        return '<span class="percentage-badge ' + badgeClass + '">' + percentage + '%</span>';
                    },
                    className: 'text-center'
                }
            ],
            ordering: true,
            searching: true,
            lengthChange: true,
            pageLength: 100,
            lengthMenu: [25, 50, 100, 200],
            order: [[6, 'desc']], // Order by percentage descending
            initComplete: function(settings, json) {
                console.log('DataTable initialization complete');
            },
            drawCallback: function(settings) {
                console.log('DataTable draw callback triggered');
            }
        });

        $('#btnFilter').on('click', function() {
            console.log('Filter button clicked');
            table.ajax.reload();
        });

        function loadSummaryData() {
            console.log('Loading summary data...');
            $.ajax({
                url: 'RekapPerDokter/get_summary_data',
                type: 'POST',
                dataType: 'json',
                data: {
                    tgl_awal: $('#tgl_awal').val(),
                    tgl_akhir: $('#tgl_akhir').val()
                },
                success: function(response) {
                    console.log('Summary response:', response);
                    try {
                        // Check if response is already an object (jQuery auto-parsed)
                        var data = typeof response === 'string' ? JSON.parse(response) : response;

                        // Check for error in response
                        if (data.error) {
                            console.error('Server error in summary:', data.error);
                            // Set default values on error
                            $('#total_dokter_count').text('0');
                            $('#total_lebih_awal_count').text('0');
                            $('#total_tepat_waktu_count').text('0');
                            $('#total_terlambat_count').text('0');
                            $('#total_pasien_count').text('0');
                            $('#rata_rata_persentase').text('0%');
                            return;
                        }

                        $('#total_dokter_count').text(data.total_dokter || 0);
                        $('#total_lebih_awal_count').text(data.total_lebih_awal || 0);
                        $('#total_tepat_waktu_count').text(data.total_tepat_waktu || 0);
                        $('#total_terlambat_count').text(data.total_terlambat || 0);
                        $('#total_pasien_count').text(data.total_pasien || 0);
                        $('#rata_rata_persentase').text((data.rata_rata_persentase || 0) + '%');
                    } catch (e) {
                        console.error('Error parsing summary response:', e);
                        console.error('Raw response:', response);
                        // Set default values on parse error
                        $('#total_dokter_count').text('0');
                        $('#total_lebih_awal_count').text('0');
                        $('#total_tepat_waktu_count').text('0');
                        $('#total_terlambat_count').text('0');
                        $('#total_pasien_count').text('0');
                        $('#rata_rata_persentase').text('0%');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Summary AJAX Error:', status, error);
                    console.error('Response Text:', xhr.responseText);
                    // Set default values on AJAX error
                    $('#total_dokter_count').text('0');
                    $('#total_lebih_awal_count').text('0');
                    $('#total_tepat_waktu_count').text('0');
                    $('#total_terlambat_count').text('0');
                    $('#total_pasien_count').text('0');
                    $('#rata_rata_persentase').text('0%');
                }
            });
        }

        function reloadDatatable() {
            $('#rekapDokterTable').DataTable().ajax.reload(null, false);
        }
        
        setInterval(reloadDatatable, 300000); // Refresh every 5 minutes

        // Clock function - only initialize if element exists and no other clock is running
        if (document.getElementById("tanggalku") && !window.clockInitialized) {
            window.clockInitialized = true;
            
            function updateClock() {
                var tanggal = new Date();
                var clockElement = document.getElementById("tanggalku");
                if (clockElement) {
                    var hours = tanggal.getHours().toString().padStart(2, '0');
                    var minutes = tanggal.getMinutes().toString().padStart(2, '0');
                    var seconds = tanggal.getSeconds().toString().padStart(2, '0');
                    clockElement.innerHTML = hours + " : " + minutes + " : " + seconds;
                }
            }
            
            // Update immediately and then every second
            updateClock();
            setInterval(updateClock, 1000);
        }
    });
}

// Initialize the rekap dokter table
initRekapDokterTable();
</script>
