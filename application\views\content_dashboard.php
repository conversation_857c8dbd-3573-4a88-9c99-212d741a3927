<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">🏥 Dashboard Monitoring Gedung Cendana</h2>
                    <p class="text-muted">Selamat datang di sistem monitoring Tower C</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">Login: <?= date('d/m/Y H:i:s') ?></small>
                </div>
            </div>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Informasi User
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-4"><strong>Nama:</strong></div>
                        <div class="col-sm-8"><?= $user_info['nama'] ?? 'N/A' ?></div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4"><strong>Username:</strong></div>
                        <div class="col-sm-8"><?= $user_info['username'] ?? 'N/A' ?></div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4"><strong>Login Time:</strong></div>
                        <div class="col-sm-8"><?= $user_info['login_time'] ?? 'N/A' ?></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Akses Menu
                    </h5>
                </div>
                <div class="card-body">
                    <?php if($has_menu_access && !empty($user_menus)): ?>
                        <p class="text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            Anda memiliki akses ke <strong><?= count($user_menus) ?> menu</strong>
                        </p>
                        <div class="small">
                            <?php foreach($user_menus as $menu): ?>
                                <span class="badge bg-secondary me-1 mb-1">
                                    <i class="fas <?= $menu['ICON'] ?> me-1"></i>
                                    <?= $menu['LABEL'] ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Anda tidak memiliki akses menu.</strong><br>
                            Silahkan hubungi administrator SIMRS untuk mendapatkan akses.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Menu -->
    <?php if($has_menu_access && !empty($user_menus)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        Quick Access
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach($user_menus as $menu): ?>
                            <?php if($menu['LINK'] != 'Dashboard'): ?>
                            <div class="col-md-3 col-sm-6 mb-3">
                                <a href="<?= base_url($menu['LINK']) ?>" class="text-decoration-none">
                                    <div class="card h-100 border-0 shadow-sm quick-access-card">
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <i class="fas <?= $menu['ICON'] ?> fa-2x text-primary"></i>
                                            </div>
                                            <h6 class="card-title"><?= $menu['LABEL'] ?></h6>
                                            <small class="text-muted">Klik untuk akses</small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- System Info -->
    <div class="row">
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="text-success">
                        <i class="fas fa-server fa-2x mb-2"></i>
                    </div>
                    <h5 class="text-success">Sistem Aktif</h5>
                    <p class="mb-0">Server berjalan normal</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="text-primary">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                    </div>
                    <h5 class="text-primary"><?= date('H:i:s') ?></h5>
                    <p class="mb-0"><?= date('d F Y') ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="text-warning">
                        <i class="fas fa-building fa-2x mb-2"></i>
                    </div>
                    <h5 class="text-warning">Tower C</h5>
                    <p class="mb-0">Gedung Cendana</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-1">📋 Sistem Monitoring Tower C</h6>
                            <p class="mb-0 text-muted">
                                Sistem monitoring untuk Gedung Cendana dengan fitur alert pasien, 
                                manajemen menu, dan monitoring DPJP.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                Version 1.0.0<br>
                                © 2024 SIMRS
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-access-card {
    transition: transform 0.2s ease-in-out;
    cursor: pointer;
}

.quick-access-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

.card {
    border-radius: 10px;
}

.badge {
    font-size: 0.75em;
}
</style>

<script>
$(document).ready(function() {
    // Update clock every second
    setInterval(function() {
        var now = new Date();
        var timeString = now.toLocaleTimeString('id-ID');
        $('.text-primary h5').first().text(timeString);
    }, 1000);
    
    // Add hover effects to quick access cards
    $('.quick-access-card').hover(
        function() {
            $(this).addClass('border-primary');
        },
        function() {
            $(this).removeClass('border-primary');
        }
    );
});
</script>
