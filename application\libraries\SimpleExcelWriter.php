<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Simple Excel Writer Library
 * Creates Excel files using HTML format that Excel can read
 */
class SimpleExcelWriter {
    
    private $sheets = array();
    private $current_sheet = null;
    
    public function __construct() {
        // Initialize
    }
    
    /**
     * Create a new sheet
     */
    public function createSheet($name) {
        $sheet = array(
            'name' => $name,
            'headers' => array(),
            'data' => array(),
            'header_style' => 'background-color: #4472C4; color: white; font-weight: bold;'
        );
        $this->sheets[] = $sheet;
        $this->current_sheet = &$this->sheets[count($this->sheets) - 1];
        return $this;
    }
    
    /**
     * Set header style for current sheet
     */
    public function setHeaderStyle($style) {
        if ($this->current_sheet) {
            $this->current_sheet['header_style'] = $style;
        }
        return $this;
    }
    
    /**
     * Add headers to current sheet
     */
    public function addHeaders($headers) {
        if ($this->current_sheet) {
            $this->current_sheet['headers'] = $headers;
        }
        return $this;
    }
    
    /**
     * Add data row to current sheet
     */
    public function addRow($data) {
        if ($this->current_sheet) {
            $this->current_sheet['data'][] = $data;
        }
        return $this;
    }
    
    /**
     * Add multiple rows to current sheet
     */
    public function addRows($rows) {
        if ($this->current_sheet && is_array($rows)) {
            foreach ($rows as $row) {
                $this->addRow($row);
            }
        }
        return $this;
    }
    
    /**
     * Generate Excel file and output to browser
     */
    public function output($filename = 'export.xls') {
        // Set headers
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        header('Pragma: public');
        
        // Generate HTML
        echo $this->generateHTML();
    }
    
    /**
     * Generate HTML content
     */
    private function generateHTML() {
        $html = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
        $html .= '<head>';
        $html .= '<meta http-equiv="Content-Type" content="text/html; charset=utf-8">';
        $html .= '<meta name="ProgId" content="Excel.Sheet">';
        $html .= '<meta name="Generator" content="Microsoft Excel 11">';
        $html .= '<style>';
        $html .= 'table { border-collapse: collapse; width: 100%; margin-bottom: 30px; }';
        $html .= 'th, td { border: 1px solid #000; padding: 5px; }';
        $html .= 'th { text-align: center; }';
        $html .= '.sheet-title { font-size: 16px; font-weight: bold; margin: 20px 0 10px 0; }';
        $html .= '</style>';
        $html .= '</head>';
        $html .= '<body>';
        
        foreach ($this->sheets as $sheet) {
            $html .= '<div class="sheet-title">' . htmlspecialchars($sheet['name']) . '</div>';
            $html .= '<table>';
            
            // Headers
            if (!empty($sheet['headers'])) {
                $html .= '<thead><tr>';
                foreach ($sheet['headers'] as $header) {
                    $html .= '<th style="' . $sheet['header_style'] . '">' . htmlspecialchars($header) . '</th>';
                }
                $html .= '</tr></thead>';
            }
            
            // Data
            if (!empty($sheet['data'])) {
                $html .= '<tbody>';
                foreach ($sheet['data'] as $row) {
                    $html .= '<tr>';
                    foreach ($row as $cell) {
                        $html .= '<td>' . htmlspecialchars($cell) . '</td>';
                    }
                    $html .= '</tr>';
                }
                $html .= '</tbody>';
            }
            
            $html .= '</table>';
        }
        
        $html .= '</body>';
        $html .= '</html>';
        
        return $html;
    }
}
