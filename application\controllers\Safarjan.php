<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>farjan extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('Model_pulang');
        $this->load->library('session');
        
        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth/login');
        }
    }

	public function index()
	{
		// $data['total'] = $this->Model_pulang->hitung();
		$this->load->view('v_safarjan');
	}

    public function get_data(){
		$draw   = intval($this->input->POST("draw"));
		$start  = intval($this->input->POST("start"));
		$length = intval($this->input->POST("length"));
	
		$listpasien = $this->Model_pulang->listFarmasi();
	
		$data = array();
		$no = 1;
		foreach ($listpasien->result() as $LP) {
			$no++;
            $data[] = array(
				$LP->NORM,
                $LP->NMPASIEN,
                $LP->RASAL,
				$LP->DESKRIPSI,
                $LP->TORDER,
                $LP->MASUK,
				$LP->NORESEP,
				$LP->STATUS == 1 ? "KUNJUNGAN AKTIF" : "-",
            );

		}
		$output = array(
            "draw"            => $draw,
            "recordsTotal"    => $listpasien->num_rows(),
            "recordsFiltered" => $listpasien->num_rows(),
            "data"            => $data
		);
		echo json_encode($output);
	}
}
