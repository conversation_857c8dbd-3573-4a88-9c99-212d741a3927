<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <?php if(isset($page_content) && $page_content == 'stockart'): ?>
    <meta name="description" content="Stockart - Sistem Manajemen Inventory" />
    <meta name="author" content="" />
    <title><?= isset($title) ? $title : 'Stockart' ?> - Sistem Manajemen Inventory</title>
    <?php else: ?>
    <meta name="description" content="Dashboard - Monitoring DPJP Praktek" />
    <meta name="author" content="" />
    <title><?= isset($title) ? $title : 'Dashboard' ?> - Monitoring DPJP Praktek</title>
    <?php endif; ?>
    <link href="https://fonts.googleapis.com/css?family=Poppins:400,500,700,800&display=swap" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/bootstrap/css/bootstrap.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/font-awesome/css/all.min.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/perfectscroll/perfect-scrollbar.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/plugins/DataTables/datatables.min.css') ?>" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css" rel="stylesheet">
    
    <!-- Theme Styles -->
    <link href="<?= base_url('assets/css/main.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/custom.css') ?>" rel="stylesheet">
    <link href="<?= base_url('assets/css/dashboard.css') ?>" rel="stylesheet">
    <!-- jQuery UI CSS -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
</head>



<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-hospital me-2"></i>
                Monitoring Gedung Cendana
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php
                    // Load menu model dan ambil menu berdasarkan hak akses user
                    if($this->session->userdata('status_login') == 'oke') {
                        try {
                            // Cek apakah Menu_model sudah di-load di controller
                            if(!isset($this->Menu_model)) {
                                $this->load->model('Menu_model');
                            }

                            $user_id = $this->session->userdata('id');
                            $user_menus = $this->Menu_model->get_user_menu($user_id);

                            if(!empty($user_menus)) {
                                foreach($user_menus as $menu) {
                                    // Skip menu Dashboard karena sudah ada di navbar-brand
                                    if($menu['LINK'] == 'Dashboard') continue;

                                    echo '<li class="nav-item">';
                                    echo '<a class="nav-link" href="' . base_url($menu['LINK']) . '">';
                                    echo '<i class="fas ' . $menu['ICON'] . ' me-1"></i>';
                                    echo $menu['LABEL'];
                                    echo '</a>';
                                    echo '</li>';
                                }
                            } else {
                                // User tidak memiliki akses menu - tampilkan pesan
                                echo '<li class="nav-item">';
                                echo '<span class="nav-link text-warning">';
                                echo '<i class="fas fa-exclamation-triangle me-1"></i>';
                                echo '<span class="badge bg-warning text-dark">Anda tidak punya Akses Menu. Silahkan Hubungi SIMRS</span>';
                                echo '</span>';
                                echo '</li>';
                            }
                        } catch (Exception $e) {
                            // Error loading menu model - fallback ke menu default
                            ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('Perjanjian') ?>">
                                    <i class="fas fa-calendar-check me-1"></i>
                                    Data Perjanjian
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('RekapPerDokter') ?>">
                                    <i class="fas fa-clock me-1"></i>
                                    Ketepatan DPJP
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('MonitoringPaCendana') ?>">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    Hasil Patologi Anatomi
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?= base_url('Stockart') ?>">
                                    <i class="fas fa-boxes me-1"></i>
                                    Stockart
                                </a>
                            </li>
                            <?php
                        }
                    }
                    ?>
                </ul>
                
                <div class="d-flex align-items-center">
                    <?php if($this->session->userdata('status_login') == 'oke'): ?>
                        <div class="user-info">
                            <i class="fas fa-user me-1"></i>
                            <span><?= $this->session->userdata('username') ?></span>
                        </div>
                        <a href="<?= base_url('Auth/logout') ?>" class="logout-btn">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            Logout
                        </a>
                    <?php else: ?>
                        <a href="<?= base_url('Auth/login') ?>" class="btn btn-outline-light">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            Login
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <?php
        if(isset($contents)) {
            echo $contents;
        } elseif(isset($page_content)) {
            // Load specific page content
            switch($page_content) {
                case 'monitoring_pa_cendana':
                    $this->load->view('content_monitoring_pa_cendana');
                    break;
                case 'rekap_per_dokter':
                    $this->load->view('content_rekap_per_dokter');
                    break;
                case 'perjanjian':
                    $this->load->view('content_perjanjian');
                    break;
                case 'stockart':
                    $this->load->view('content_stockart');
                    break;
                case 'alert_pasien':
                    $this->load->view('content_alert_pasien');
                    break;
                case 'admin_menu':
                    $this->load->view('content_admin_menu');
                    break;
                case 'dashboard':
                    $this->load->view('content_dashboard');
                    break;
                default:
                    echo '<div class="dashboard-welcome">';
                    echo '<div class="container-fluid">';
                    echo '<div class="row">';
                    echo '<div class="col-12 text-center">';
                    echo '<h3>Selamat Datang di Sistem Monitoring Gedung Cendana</h3>';
                    echo '<p>Silakan pilih menu di atas untuk melihat data monitoring</p>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                    break;
            }
        } else {
            echo '<div class="dashboard-welcome">';
            echo '<div class="container-fluid">';
            echo '<div class="row">';
            echo '<div class="col-12 text-center">';
            echo '<h3>Selamat Datang di Sistem Monitoring Gedung Cendana</h3>';
            echo '<p>Silakan pilih menu di atas untuk melihat data monitoring</p>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        ?>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?= date('Y') ?> RSK Dharmais Gedung Cendana. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Sistem Monitoring DPJP Praktek v1.0</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="<?= base_url('assets/js/jquery-3.7.1.min.js') ?>"></script>
    <!-- Bootstrap JS - Use local version to match CSS -->
    <script src="<?= base_url('assets/plugins/bootstrap/js/bootstrap.bundle.min.js') ?>"></script>
    <!-- jQuery UI - Load after jQuery -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="<?= base_url('assets/plugins/perfectscroll/perfect-scrollbar.min.js') ?>"></script>
    <script src="<?= base_url('assets/plugins/DataTables/datatables.min.js') ?>"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="<?= base_url('assets/js/main.min.js') ?>"></script>

    <?php if(isset($page_content) && $page_content == 'admin_menu'): ?>
    <!-- Debug script for AdminMenu -->
    <script src="<?= base_url('assets/js/debug-admin-menu.js') ?>"></script>
    <script src="<?= base_url('assets/js/petugas-debug.js') ?>"></script>
    <?php endif; ?>

    <script>
        $(document).ready(function() {
            try {
                if (typeof feather !== 'undefined') {
                    feather.replace();
                }
            } catch (e) {
            }

            // Set active navigation item based on current URL
            var currentUrl = window.location.pathname;
            $('.navbar-nav .nav-link').each(function() {
                var linkUrl = $(this).attr('href');
                if (currentUrl.includes(linkUrl.split('/').pop())) {
                    $(this).addClass('active');
                }
            });

            // Logout confirmation
            $('.logout-btn').on('click', function(e) {
                if (!confirm('Apa kamu yakin ingin logout?')) {
                    e.preventDefault();
                }
            });

            // Auto-hide alerts
            $('.alert').delay(5000).fadeOut();

            // Enhanced navbar toggle with fallback
            function initNavbarToggle() {
                try {
                    if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
                        // Pure Bootstrap 5 navbar toggle - remove all custom handlers
                        $('.navbar-toggler').off('click');
                        console.log('Using Bootstrap 5 native navbar toggle');
                    } else {
                        // Fallback: Manual toggle if Bootstrap is not available
                        $('.navbar-toggler').off('click').on('click', function(e) {
                            e.preventDefault();
                            var $collapse = $('#navbarNav');
                            var $toggler = $(this);

                            if ($collapse.hasClass('show')) {
                                $collapse.removeClass('show');
                                $toggler.attr('aria-expanded', 'false');
                            } else {
                                $collapse.addClass('show');
                                $toggler.attr('aria-expanded', 'true');
                            }
                        });
                        console.log('Using manual navbar toggle fallback');
                    }
                } catch (error) {
                    console.error('Error in initNavbarToggle:', error);
                }
            }

            // Initialize navbar toggle
            initNavbarToggle();

            // Close navbar when clicking on nav links (responsive)
            $('.navbar-nav .nav-link').off('click.navbar').on('click.navbar', function(e) {
                if (window.innerWidth < 992) { // Bootstrap lg breakpoint
                    var $collapse = $('#navbarNav');
                    var $toggler = $('.navbar-toggler');

                    // Use Bootstrap 5 Collapse API if available
                    if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
                        try {
                            // Try getOrCreateInstance first (Bootstrap 5.1+)
                            if (typeof bootstrap.Collapse.getOrCreateInstance === 'function') {
                                var collapseInstance = bootstrap.Collapse.getOrCreateInstance($collapse[0]);
                                collapseInstance.hide();
                            } else {
                                // Fallback for older Bootstrap 5 versions
                                var collapseInstance = new bootstrap.Collapse($collapse[0], {toggle: false});
                                collapseInstance.hide();
                            }
                        } catch (e) {
                            console.log('Bootstrap Collapse error, using manual fallback:', e);
                            $collapse.removeClass('show');
                            $toggler.attr('aria-expanded', 'false');
                        }
                    } else {
                        // Fallback: Manual close
                        $collapse.removeClass('show');
                        $toggler.attr('aria-expanded', 'false');
                    }
                }
            });

            // Close navbar when clicking outside (responsive)
            $(document).off('click.navbar').on('click.navbar', function(e) {
                if (window.innerWidth < 992) { // Bootstrap lg breakpoint
                    var $collapse = $('#navbarNav');
                    var $toggler = $('.navbar-toggler');

                    if (!$(e.target).closest('.navbar').length && $collapse.hasClass('show')) {

                        // Use Bootstrap 5 Collapse API if available
                        if (typeof bootstrap !== 'undefined' && bootstrap.Collapse) {
                            try {
                                // Try getOrCreateInstance first (Bootstrap 5.1+)
                                if (typeof bootstrap.Collapse.getOrCreateInstance === 'function') {
                                    var collapseInstance = bootstrap.Collapse.getOrCreateInstance($collapse[0]);
                                    collapseInstance.hide();
                                } else {
                                    // Fallback for older Bootstrap 5 versions
                                    var collapseInstance = new bootstrap.Collapse($collapse[0], {toggle: false});
                                    collapseInstance.hide();
                                }
                            } catch (e) {
                                console.log('Bootstrap Collapse error, using manual fallback:', e);
                                $collapse.removeClass('show');
                                $toggler.attr('aria-expanded', 'false');
                            }
                        } else {
                            // Fallback: Manual close
                            $collapse.removeClass('show');
                            $toggler.attr('aria-expanded', 'false');
                        }
                    }
                }
            });

            // Handle window resize to ensure proper navbar behavior
            $(window).off('resize.navbar').on('resize.navbar', function() {
                var $collapse = $('#navbarNav');
                var $toggler = $('.navbar-toggler');

                if (window.innerWidth >= 992) { // Bootstrap lg breakpoint
                    // Desktop: ensure navbar is always visible and reset state
                    $collapse.removeClass('show');
                    $toggler.attr('aria-expanded', 'false');
                } else {
                    // Tablet/Mobile: maintain current state
                    if ($collapse.hasClass('show')) {
                        $toggler.attr('aria-expanded', 'true');
                    } else {
                        $toggler.attr('aria-expanded', 'false');
                    }
                }
            });

            // Prevent menu from closing when clicking inside the menu
            $('#navbarNav').off('click.navbar').on('click.navbar', function(e) {
                e.stopPropagation();
            });

            // Bootstrap collapse event listeners for debugging
            $('#navbarNav').on('shown.bs.collapse', function () {
            });

            $('#navbarNav').on('hidden.bs.collapse', function () {
            });
        });
    </script>
</body>
</html>
