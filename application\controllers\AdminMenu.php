<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AdminMenu extends CI_Controller {

    function __construct() {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->model('Menu_model');
        $this->load->model('Petugas_model');
        $this->load->library('session');

        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth/login');
        }

        // Cek hak akses menu admin
        $user_id = $this->session->userdata('id');
        if(!$this->Menu_model->check_user_access($user_id, 'AdminMenu')) {
            show_error('Anda tidak memiliki akses ke halaman ini.', 403, 'Aks<PERSON>');
        }
    }

    public function index() {
        $data['title'] = 'Admin Menu - Kelola Hak Akses';
        $data['page_content'] = 'admin_menu';

        // Ambil semua menu untuk modal
        $data['all_menus'] = $this->Menu_model->get_all_menu();

        $this->load->view('template_dashboard', $data);
    }

    /**
     * AJAX endpoint untuk DataTables serverside
     */
    public function get_users_data() {
        $draw = $this->input->post('draw');
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $search_value = $this->input->post('search')['value'];
        $order_column = $this->input->post('order')[0]['column'];
        $order_dir = $this->input->post('order')[0]['dir'];

        // Column mapping
        $columns = array('ID', 'LOGIN', 'NAMA', 'total_menu_access');
        $order_column_name = $columns[$order_column];

        // Get data
        $users_data = $this->Menu_model->get_users_datatables($start, $length, $search_value, $order_column_name, $order_dir);
        $total_users = $this->Menu_model->count_all_users();
        $filtered_users = $this->Menu_model->count_filtered_users($search_value);

        $data = array();
        $no = $start + 1;

        foreach ($users_data as $user) {
            $row = array();
            $row[] = $no++;
            $row[] = '<strong>' . $user['LOGIN'] . '</strong><br><small class="text-muted">ID: ' . $user['ID'] . '</small>';
            $row[] = $user['NAMA'];
            $row[] = '<span class="badge bg-info">' . $user['total_menu_access'] . ' Menu</span>';
            $row[] = $user['total_menu_access'] > 0 ?
                     '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Aktif</span>' :
                     '<span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i>Tidak Ada Akses</span>';
            $row[] = '<div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-primary" onclick="editUserAccess(' . $user['ID'] . ')" title="Edit Hak Akses">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="resetAccess(' . $user['ID'] . ')" title="Reset Akses">
                            <i class="fas fa-trash"></i>
                        </button>
                      </div>';

            $data[] = $row;
        }

        $output = array(
            "draw" => intval($draw),
            "recordsTotal" => $total_users,
            "recordsFiltered" => $filtered_users,
            "data" => $data
        );

        echo json_encode($output);
    }

    /**
     * Halaman edit hak akses user
     */
    public function edit_user_access($user_id) {
        $data['title'] = 'Edit Hak Akses User';
        $data['page_content'] = 'admin_menu_edit';
        
        // Ambil data user
        $data['user'] = $this->Menu_model->get_user_with_menu_access($user_id);
        
        if(!$data['user']) {
            show_404();
        }
        
        // Ambil semua menu dengan informasi akses user
        $data['menus'] = $this->Menu_model->get_menu_with_user_access($user_id);
        
        $this->load->view('template_dashboard', $data);
    }

    /**
     * Proses update hak akses user (AJAX)
     */
    public function update_user_access() {
        // Set header untuk JSON response
        header('Content-Type: application/json');

        $user_id = $this->input->post('user_id');
        $menu_ids = $this->input->post('menu_ids');

        if(!$user_id) {
            echo json_encode(array(
                'success' => false,
                'message' => 'User ID tidak valid.'
            ));
            return;
        }

        // Set hak akses menu untuk user
        $result = $this->Menu_model->set_user_menu_access($user_id, $menu_ids ?: array());

        if($result) {
            echo json_encode(array(
                'success' => true,
                'message' => 'Hak akses user berhasil diperbarui.'
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Gagal memperbarui hak akses user.'
            ));
        }
    }

    /**
     * AJAX endpoint untuk toggle akses menu user
     */
    public function toggle_user_menu_access() {
        $user_id = $this->input->post('user_id');
        $menu_id = $this->input->post('menu_id');
        $action = $this->input->post('action'); // 'add' atau 'remove'
        
        if(!$user_id || !$menu_id || !in_array($action, ['add', 'remove'])) {
            echo json_encode(array('success' => false, 'message' => 'Parameter tidak valid'));
            return;
        }
        
        if($action == 'add') {
            $result = $this->Menu_model->add_user_menu_access($user_id, $menu_id);
        } else {
            $result = $this->Menu_model->remove_user_menu_access($user_id, $menu_id);
        }
        
        if($result) {
            echo json_encode(array('success' => true, 'message' => 'Akses berhasil diperbarui'));
        } else {
            echo json_encode(array('success' => false, 'message' => 'Gagal memperbarui akses'));
        }
    }

    /**
     * Copy akses menu dari user lain
     */
    public function copy_user_access() {
        $source_user_id = $this->input->post('source_user_id');
        $target_user_id = $this->input->post('target_user_id');
        
        if(!$source_user_id || !$target_user_id) {
            $this->session->set_flashdata('error', 'Parameter tidak valid.');
            redirect('AdminMenu');
            return;
        }
        
        // Ambil akses menu dari source user
        $source_menu_access = $this->Menu_model->get_user_menu_access($source_user_id);
        
        // Set akses menu untuk target user
        $result = $this->Menu_model->set_user_menu_access($target_user_id, $source_menu_access);
        
        if($result) {
            $this->session->set_flashdata('success', 'Hak akses berhasil disalin.');
        } else {
            $this->session->set_flashdata('error', 'Gagal menyalin hak akses.');
        }
        
        redirect('AdminMenu/edit_user_access/' . $target_user_id);
    }

    /**
     * Reset akses menu user (hapus semua akses)
     */
    public function reset_user_access($user_id) {
        if(!$user_id) {
            $this->session->set_flashdata('error', 'User ID tidak valid.');
            redirect('AdminMenu');
            return;
        }
        
        $result = $this->Menu_model->set_user_menu_access($user_id, array());
        
        if($result) {
            $this->session->set_flashdata('success', 'Hak akses user berhasil direset.');
        } else {
            $this->session->set_flashdata('error', 'Gagal mereset hak akses user.');
        }
        
        redirect('AdminMenu/edit_user_access/' . $user_id);
    }

    /**
     * AJAX endpoint untuk mendapatkan data user dan menu access untuk modal
     */
    public function get_user_menu_data() {
        $user_id = $this->input->post('user_id');

        if (!$user_id) {
            echo json_encode(array('success' => false, 'message' => 'User ID tidak valid'));
            return;
        }

        // Ambil data user
        $user = $this->Menu_model->get_user_with_menu_access($user_id);

        if (!$user) {
            echo json_encode(array('success' => false, 'message' => 'User tidak ditemukan'));
            return;
        }

        // Ambil semua menu dengan informasi akses user
        $menus = $this->Menu_model->get_menu_with_user_access($user_id);

        echo json_encode(array(
            'success' => true,
            'user' => $user,
            'menus' => $menus
        ));
    }

    /**
     * AJAX endpoint untuk mendapatkan statistik users
     */
    public function get_user_stats() {
        $total_users = $this->Menu_model->count_all_users();
        $users_with_access = $this->Menu_model->count_users_with_access();
        $users_without_access = $total_users - $users_with_access;

        echo json_encode(array(
            'total_users' => $total_users,
            'active_users' => $users_with_access,
            'inactive_users' => $users_without_access
        ));
    }

    /**
     * Test connection
     */
    public function test_connection() {
        echo "AdminMenu Controller is working! Current time: " . date('Y-m-d H:i:s');
    }

    // ==================== PETUGAS MANAGEMENT ====================

    /**
     * AJAX endpoint untuk DataTables petugas
     */
    public function get_petugas_data() {
        $draw = $this->input->post('draw');
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $search_value = $this->input->post('search')['value'];
        $order_column = $this->input->post('order')[0]['column'];
        $order_dir = $this->input->post('order')[0]['dir'];

        // Get data
        $petugas_data = $this->Petugas_model->get_petugas_datatables($start, $length, $search_value, $order_column, $order_dir);
        $total_petugas = $this->Petugas_model->count_all_petugas();
        $filtered_petugas = $this->Petugas_model->count_filtered_petugas($search_value);

        $data = array();

        foreach ($petugas_data as $petugas) {
            $row = array(
                'id' => $petugas['ID'],
                'no_absen' => $petugas['NO_ABSEN'],
                'nama' => $petugas['NAMA'],
                'nomor_hp' => $petugas['NOMOR_HP'],
                'status' => $petugas['STATUS'] == 1 ? 'aktif' : 'nonaktif'
            );
            $data[] = $row;
        }

        $output = array(
            "draw" => intval($draw),
            "recordsTotal" => $total_petugas,
            "recordsFiltered" => $filtered_petugas,
            "data" => $data
        );

        echo json_encode($output);
    }

    /**
     * Cari pegawai berdasarkan No Absen
     */
    public function search_pegawai_by_absen() {
        header('Content-Type: application/json');

        $no_absen = $this->input->post('no_absen');

        if (!$no_absen) {
            echo json_encode(array(
                'success' => false,
                'message' => 'No Absen tidak boleh kosong.'
            ));
            return;
        }

        // Cek apakah No Absen sudah ada di tabel petugas
        if ($this->Petugas_model->check_absen_exists($no_absen)) {
            echo json_encode(array(
                'success' => false,
                'message' => 'No Absen sudah terdaftar sebagai petugas.'
            ));
            return;
        }

        // Cari pegawai di database 238
        $pegawai = $this->Petugas_model->get_pegawai_by_absen($no_absen);

        if ($pegawai) {
            echo json_encode(array(
                'success' => true,
                'data' => $pegawai
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Pegawai dengan No Absen tersebut tidak ditemukan.'
            ));
        }
    }

    /**
     * Simpan petugas baru
     */
    public function save_petugas() {
        header('Content-Type: application/json');

        $no_absen = $this->input->post('no_absen');
        $nama = $this->input->post('nama');
        $nomor_hp = $this->input->post('nomor_hp');

        // Validasi input
        if (!$no_absen || !$nama || !$nomor_hp) {
            echo json_encode(array(
                'success' => false,
                'message' => 'Semua field harus diisi.'
            ));
            return;
        }

        // Cek apakah No Absen sudah ada
        if ($this->Petugas_model->check_absen_exists($no_absen)) {
            echo json_encode(array(
                'success' => false,
                'message' => 'No Absen sudah terdaftar sebagai petugas.'
            ));
            return;
        }

        $data = array(
            'no_absen' => $no_absen,
            'nama' => $nama,
            'nomor_hp' => $nomor_hp
        );

        $result = $this->Petugas_model->save_petugas($data);

        if ($result) {
            echo json_encode(array(
                'success' => true,
                'message' => 'Petugas berhasil ditambahkan.'
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Gagal menambahkan petugas.'
            ));
        }
    }

    /**
     * Ambil data petugas untuk edit
     */
    public function get_petugas_data_edit() {
        header('Content-Type: application/json');

        $id = $this->input->post('id');

        if (!$id) {
            echo json_encode(array(
                'success' => false,
                'message' => 'ID petugas tidak valid.'
            ));
            return;
        }

        $petugas = $this->Petugas_model->get_petugas_by_id($id);

        if ($petugas) {
            echo json_encode(array(
                'success' => true,
                'data' => $petugas
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Petugas tidak ditemukan.'
            ));
        }
    }

    /**
     * Update petugas
     */
    public function update_petugas() {
        header('Content-Type: application/json');

        $id = $this->input->post('id');
        $no_absen = $this->input->post('no_absen');
        $nama = $this->input->post('nama');
        $nomor_hp = $this->input->post('nomor_hp');
        $status = $this->input->post('status');

        // Validasi input
        if (!$id || !$no_absen || !$nama || !$nomor_hp) {
            echo json_encode(array(
                'success' => false,
                'message' => 'Semua field harus diisi.'
            ));
            return;
        }

        // Cek apakah No Absen sudah ada (kecuali untuk petugas yang sedang diedit)
        if ($this->Petugas_model->check_absen_exists($no_absen, $id)) {
            echo json_encode(array(
                'success' => false,
                'message' => 'No Absen sudah terdaftar sebagai petugas lain.'
            ));
            return;
        }

        $data = array(
            'no_absen' => $no_absen,
            'nama' => $nama,
            'nomor_hp' => $nomor_hp,
            'status' => $status
        );

        $result = $this->Petugas_model->update_petugas($id, $data);

        if ($result) {
            echo json_encode(array(
                'success' => true,
                'message' => 'Petugas berhasil diperbarui.'
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Gagal memperbarui petugas.'
            ));
        }
    }

    /**
     * Toggle status petugas
     */
    public function toggle_status_petugas() {
        header('Content-Type: application/json');

        $id = $this->input->post('id');
        $status = $this->input->post('status');

        if (!$id || !in_array($status, [0, 1])) {
            echo json_encode(array(
                'success' => false,
                'message' => 'Parameter tidak valid.'
            ));
            return;
        }

        $result = $this->Petugas_model->update_status_petugas($id, $status);

        if ($result) {
            $status_text = $status == 1 ? 'diaktifkan' : 'dinonaktifkan';
            echo json_encode(array(
                'success' => true,
                'message' => 'Petugas berhasil ' . $status_text . '.'
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Gagal mengubah status petugas.'
            ));
        }
    }

    /**
     * Ambil statistik petugas
     */
    public function get_petugas_stats() {
        header('Content-Type: application/json');

        $stats = $this->Petugas_model->get_petugas_stats();
        echo json_encode($stats);
    }
}
