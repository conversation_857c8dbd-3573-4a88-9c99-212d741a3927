<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Petugas_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Ambil semua petugas untuk DataTables
     */
    public function get_petugas_datatables($start, $length, $search_value, $order_column, $order_dir) {
        $this->db->select('ID, NO_ABSEN, NAMA, NOMOR_HP, STATUS, CREATED_AT, UPDATED_AT');
        $this->db->from('aplikasi.towerc_petugas');

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('NO_ABSEN', $search_value);
            $this->db->or_like('NAMA', $search_value);
            $this->db->or_like('NOMOR_HP', $search_value);
            $this->db->group_end();
        }

        // Column mapping untuk sorting
        $columns = array('ID', 'NO_ABSEN', 'NAMA', 'NOMOR_HP', 'STATUS');
        if (isset($columns[$order_column])) {
            $this->db->order_by($columns[$order_column], $order_dir);
        } else {
            $this->db->order_by('NAMA', 'ASC');
        }

        $this->db->limit($length, $start);

        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Hitung total petugas
     */
    public function count_all_petugas() {
        $this->db->from('aplikasi.towerc_petugas');
        return $this->db->count_all_results();
    }

    /**
     * Hitung petugas yang difilter
     */
    public function count_filtered_petugas($search_value) {
        $this->db->from('aplikasi.towerc_petugas');

        if (!empty($search_value)) {
            $this->db->group_start();
            $this->db->like('NO_ABSEN', $search_value);
            $this->db->or_like('NAMA', $search_value);
            $this->db->or_like('NOMOR_HP', $search_value);
            $this->db->group_end();
        }

        return $this->db->count_all_results();
    }

    /**
     * Ambil petugas berdasarkan ID
     */
    public function get_petugas_by_id($id) {
        $this->db->select('*');
        $this->db->from('aplikasi.towerc_petugas');
        $this->db->where('ID', $id);

        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Cari pegawai berdasarkan No Absen dari database 238
     */
    public function get_pegawai_by_absen($no_absen) {
        // Load database 238
        $db238 = $this->load->database('238', TRUE);
        
        $db238->select('
            peg.ABSEN,
            CONCAT(
                COALESCE(peg.GELAR_DEPAN, ""), 
                IF(peg.GELAR_DEPAN IS NOT NULL AND peg.GELAR_DEPAN != "", " ", ""),
                peg.NAMA_LENGKAP,
                IF(peg.GELAR_BELAKANG IS NOT NULL AND peg.GELAR_BELAKANG != "", " ", ""),
                COALESCE(peg.GELAR_BELAKANG, "")
            ) as NAMA_LENGKAP,
            peg.NOMOR_HP
        ');
        $db238->from('dbsdm.pegawai1 peg');
        $db238->where('peg.ABSEN', $no_absen);

        $query = $db238->get();
        $result = $query->row_array();
        
        $db238->close();
        return $result;
    }

    /**
     * Cek apakah No Absen sudah ada di tabel petugas
     */
    public function check_absen_exists($no_absen, $exclude_id = null) {
        $this->db->select('COUNT(*) as count');
        $this->db->from('aplikasi.towerc_petugas');
        $this->db->where('NO_ABSEN', $no_absen);
        
        if ($exclude_id) {
            $this->db->where('ID !=', $exclude_id);
        }

        $query = $this->db->get();
        $result = $query->row_array();
        
        return (int)$result['count'] > 0;
    }

    /**
     * Simpan petugas baru
     */
    public function save_petugas($data) {
        $petugas_data = array(
            'NO_ABSEN' => $data['no_absen'],
            'NAMA' => $data['nama'],
            'NOMOR_HP' => $data['nomor_hp'],
            'STATUS' => 1,
            'CREATED_AT' => date('Y-m-d H:i:s'),
            'UPDATED_AT' => date('Y-m-d H:i:s')
        );

        $this->db->insert('aplikasi.towerc_petugas', $petugas_data);
        return $this->db->insert_id();
    }

    /**
     * Update petugas
     */
    public function update_petugas($id, $data) {
        $petugas_data = array(
            'NO_ABSEN' => $data['no_absen'],
            'NAMA' => $data['nama'],
            'NOMOR_HP' => $data['nomor_hp'],
            'UPDATED_AT' => date('Y-m-d H:i:s')
        );

        if (isset($data['status'])) {
            $petugas_data['STATUS'] = $data['status'];
        }

        $this->db->where('ID', $id);
        return $this->db->update('aplikasi.towerc_petugas', $petugas_data);
    }

    /**
     * Update status petugas
     */
    public function update_status_petugas($id, $status) {
        $this->db->where('ID', $id);
        return $this->db->update('aplikasi.towerc_petugas', array(
            'STATUS' => $status,
            'UPDATED_AT' => date('Y-m-d H:i:s')
        ));
    }

    /**
     * Hapus petugas (soft delete dengan mengubah status jadi 0)
     */
    public function delete_petugas($id) {
        return $this->update_status_petugas($id, 0);
    }

    /**
     * Ambil statistik petugas
     */
    public function get_petugas_stats() {
        // Total petugas
        $this->db->select('COUNT(*) as total');
        $this->db->from('aplikasi.towerc_petugas');
        $total = $this->db->get()->row_array()['total'];

        // Petugas aktif
        $this->db->select('COUNT(*) as aktif');
        $this->db->from('aplikasi.towerc_petugas');
        $this->db->where('STATUS', 1);
        $aktif = $this->db->get()->row_array()['aktif'];

        // Petugas nonaktif
        $nonaktif = $total - $aktif;

        return array(
            'total' => $total,
            'aktif' => $aktif,
            'nonaktif' => $nonaktif
        );
    }

    /**
     * Ambil semua petugas aktif untuk dropdown
     */
    public function get_active_petugas() {
        $this->db->select('ID, NAMA, NO_ABSEN, NOMOR_HP');
        $this->db->from('aplikasi.towerc_petugas');
        $this->db->where('STATUS', 1);
        $this->db->order_by('NAMA', 'ASC');

        $query = $this->db->get();
        return $query->result_array();
    }
}
