// Debug script untuk AdminMenu
console.log('Debug script loaded');

// Test form submission manually
function testFormSubmit() {
    console.log('Testing form submit...');
    
    // Check if form exists
    var form = $('#editAccessForm');
    console.log('Form exists:', form.length > 0);
    
    if (form.length > 0) {
        console.log('Form HTML:', form[0].outerHTML);
        
        // Trigger submit event
        form.trigger('submit');
    } else {
        console.log('Form not found');
    }
}

// Test modal
function testModal() {
    console.log('Testing modal...');
    $('#editAccessModal').modal('show');
}

// Test AJAX directly
function testAjax() {
    console.log('Testing AJAX...');
    
    $.ajax({
        url: window.location.origin + '/towercmonitoring/AdminMenu/get_user_menu_data',
        type: 'POST',
        data: { user_id: 2597 },
        dataType: 'json',
        success: function(response) {
            console.log('Direct AJAX Success:', response);
        },
        error: function(xhr, status, error) {
            console.error('Direct AJAX Error:', {xhr, status, error});
            console.error('Response Text:', xhr.responseText);
        }
    });
}

// Test form submit directly
function testDirectSubmit() {
    console.log('Testing direct form submit...');
    
    var formData = 'user_id=2597&menu_ids[]=1&menu_ids[]=2&menu_ids[]=3';
    
    $.ajax({
        url: window.location.origin + '/towercmonitoring/AdminMenu/update_user_access',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            console.log('Direct Submit Success:', response);
        },
        error: function(xhr, status, error) {
            console.error('Direct Submit Error:', {xhr, status, error});
            console.error('Response Text:', xhr.responseText);
        }
    });
}

// Make functions available globally
window.testFormSubmit = testFormSubmit;
window.testModal = testModal;
window.testAjax = testAjax;
window.testDirectSubmit = testDirectSubmit;

console.log('Debug functions available: testFormSubmit(), testModal(), testAjax(), testDirectSubmit()');
