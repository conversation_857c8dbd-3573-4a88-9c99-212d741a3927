<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AlertPasien extends CI_Controller {

    function __construct() {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->library('form_validation');
        $this->load->model('AlertPasien_model');
        $this->load->model('Menu_model');
        $this->load->library('Whatsapp');

        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth/login');
        }

        // Cek hak akses menu
        $user_id = $this->session->userdata('id');
        if(!$this->Menu_model->check_user_access($user_id, 'AlertPasien')) {
            show_error('Anda tidak memiliki akses ke halaman ini.', 403, '<PERSON>ks<PERSON>lak');
        }
    }

    public function index() {
        $data['title'] = 'Alert Pasien';
        $data['page_content'] = 'alert_pasien';
        
        // Ambil data petugas untuk checkbox
        $data['petugas_list'] = $this->AlertPasien_model->get_all_petugas();
        
        // Ambil statistik alert
        $data['alert_stats'] = $this->AlertPasien_model->get_alert_statistics();
        
        $this->load->view('template_dashboard', $data);
    }

    /**
     * AJAX endpoint untuk autocomplete pasien
     */
    public function autocomplete_pasien() {
        $term = $this->input->get('term');
        
        if(empty($term)) {
            echo json_encode(array());
            return;
        }
        
        $pasien_list = $this->AlertPasien_model->get_pasien_autocomplete($term);
        
        $result = array();
        foreach($pasien_list as $pasien) {
            $result[] = array(
                'id' => $pasien['NORM'],
                'label' => $pasien['NORM'] . ' - ' . $pasien['NAMA'],
                'value' => $pasien['NORM'] . ' - ' . $pasien['NAMA'],
                'norm' => $pasien['NORM'],
                'nama' => $pasien['NAMA']
            );
        }
        
        header('Content-Type: application/json');
        echo json_encode($result);
    }

    /**
     * AJAX endpoint untuk mendapatkan data pasien berdasarkan NORM
     */
    public function get_pasien_by_norm() {
        $norm = $this->input->post('norm');
        
        if(empty($norm)) {
            echo json_encode(array('success' => false, 'message' => 'NORM tidak boleh kosong'));
            return;
        }
        
        $pasien = $this->AlertPasien_model->get_pasien_by_norm($norm);
        
        if($pasien) {
            echo json_encode(array(
                'success' => true,
                'data' => $pasien
            ));
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Pasien tidak ditemukan'
            ));
        }
    }

    /**
     * Proses submit alert pasien
     */
    public function submit_alert() {
        // Set header untuk JSON response
        header('Content-Type: application/json');

        // Validasi input
        $this->form_validation->set_rules('norm', 'No. MR', 'required|trim');
        $this->form_validation->set_rules('nama_pasien', 'Nama Pasien', 'required|trim');
        $this->form_validation->set_rules('kategori', 'Kategori', 'required|in_list[High,Medium,Low]');
        $this->form_validation->set_rules('keterangan', 'Keterangan', 'required|trim');
        $this->form_validation->set_rules('petugas[]', 'Petugas', 'required');

        if ($this->form_validation->run() == FALSE) {
            echo json_encode(array(
                'success' => false,
                'message' => 'Validasi gagal: ' . strip_tags(validation_errors())
            ));
            return;
        }
        
        $norm = $this->input->post('norm');
        $nama_pasien = $this->input->post('nama_pasien');
        $kategori = $this->input->post('kategori');
        $keterangan = $this->input->post('keterangan');
        $petugas_ids = $this->input->post('petugas');
        
        // Cek apakah pasien sudah pernah di-alert hari ini
        // Jika status WhatsApp sebelumnya "sent", blokir. Jika "failed", tetap boleh kirim.
        $already_alerted = $this->AlertPasien_model->check_duplicate_alert_today($norm);
        $can_send = true;
        if ($already_alerted) {
            // Cari ALERT_ID dari alert hari ini untuk NORM ini
            $today = date('Y-m-d');
            $alert_ids = $this->db->select('ID')
                ->from('log.towerc_alert_pasien')
                ->where('NORM', $norm)
                ->where('DATE(CREATED_AT)', $today)
                ->get()->result_array();

            $can_send = true;
            if (!empty($alert_ids)) {
                foreach ($alert_ids as $alert_row) {
                    $alert_id = $alert_row['ID'];
                    $alert_logs = $this->db->select('STATUS')
                        ->from('log.towerc_whatsapp_log')
                        ->where('ALERT_ID', $alert_id)
                        ->get()->result_array();

                    foreach ($alert_logs as $log) {
                        if (isset($log['STATUS']) && strtolower($log['STATUS']) === 'sent') {
                            $can_send = false;
                            break 2;
                        }
                    }
                }
            }
            if (!$can_send) {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'Pasien dengan NORM ' . $norm . ' sudah pernah di-alert hari ini dan sudah berhasil dikirim WA.'
                ));
                return;
            }
            // Jika semua status failed, lanjutkan proses kirim ulang
        }
        
        // Simpan alert
        $alert_data = array(
            'norm' => $norm,
            'nama_pasien' => $nama_pasien,
            'kategori' => $kategori,
            'keterangan' => $keterangan
        );
        
        $alert_id = $this->AlertPasien_model->save_alert($alert_data);
        
        if($alert_id) {
            // Kirim WhatsApp ke petugas yang dipilih
            $success_count = 0;
            $failed_count = 0;
            
            foreach($petugas_ids as $petugas_id) {
                $petugas = $this->AlertPasien_model->get_petugas_by_id($petugas_id);
                
                if($petugas) {
                    // Format pesan WhatsApp
                    $pesan = $this->format_whatsapp_message($norm, $nama_pasien, $kategori, $keterangan);
                    
                    // Format nomor HP ke format internasional +62
                    $nomor_hp = $petugas['NOMOR_HP'];
                    if (substr($nomor_hp, 0, 1) == '0') {
                        $nomor_hp = '+62' . substr($nomor_hp, 1);
                    } elseif (substr($nomor_hp, 0, 3) != '+62') {
                        $nomor_hp = '+62' . $nomor_hp;
                    }
                    
                    // Kirim WhatsApp
                    $whatsapp_result = $this->whatsapp->send($nomor_hp, $pesan);
                    
                    // Debug: log response untuk troubleshooting
                    error_log("WhatsApp Response: " . json_encode($whatsapp_result));
                    
                    // Tentukan status berdasarkan response
                    $status = 'failed';
                    // Cek response API - data.data[0].status adalah field yang menentukan
                    if(isset($whatsapp_result['data']) && 
                       isset($whatsapp_result['data']['data']) && 
                       is_array($whatsapp_result['data']['data']) &&
                       count($whatsapp_result['data']['data']) > 0) {
                        $api_status = $whatsapp_result['data']['data'][0]['status'];
                        if(strtolower($api_status) == 'sent') {
                            $status = 'sent';
                            $success_count++;
                        } else {
                            $failed_count++;
                        }
                    } else {
                        $failed_count++;
                    }
                    
                    // Simpan log
                    $this->AlertPasien_model->save_whatsapp_log(
                        $alert_id, 
                        $petugas_id, 
                        $nomor_hp, 
                        $pesan, 
                        $status, 
                        $whatsapp_result
                    );
                }
            }
            
            // Return JSON response berdasarkan hasil pengiriman
            if($success_count > 0 && $failed_count == 0) {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'Alert berhasil disimpan dan dikirim ke ' . $success_count . ' petugas.'
                ));
            } elseif($success_count > 0 && $failed_count > 0) {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'Alert berhasil disimpan. Berhasil dikirim ke ' . $success_count . ' petugas, gagal ke ' . $failed_count . ' petugas.',
                    'type' => 'warning'
                ));
            } else {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'Alert berhasil disimpan tetapi gagal mengirim WhatsApp ke semua petugas.',
                    'type' => 'warning'
                ));
            }
        } else {
            echo json_encode(array(
                'success' => false,
                'message' => 'Gagal menyimpan alert pasien.'
            ));
        }
    }

    /**
     * AJAX endpoint untuk DataTables riwayat alert
     */
    public function get_riwayat_data() {
        // Set header untuk JSON response
        header('Content-Type: application/json');

        $draw = $this->input->post('draw');
        $start = $this->input->post('start');
        $length = $this->input->post('length');
        $search_value = $this->input->post('search')['value'];
        $order_column = $this->input->post('order')[0]['column'];
        $order_dir = $this->input->post('order')[0]['dir'];

        // Column mapping
        $columns = array('id', 'created_at', 'norm', 'nama_pasien', 'kategori', 'keterangan', 'status');
        $order_column_name = $columns[$order_column];

        try {
            // Get data
            $riwayat_data = $this->AlertPasien_model->get_riwayat_datatables($start, $length, $search_value, $order_column_name, $order_dir);
            $total_riwayat = $this->AlertPasien_model->count_all_riwayat();
            $filtered_riwayat = $this->AlertPasien_model->count_filtered_riwayat($search_value);

            $data = array();
            $no = $start + 1;

            foreach ($riwayat_data as $alert) {
                $row = array();
                $row[] = $no++;
                $row[] = date('d/m/Y H:i', strtotime($alert['created_at']));
                $row[] = '<strong>' . $alert['norm'] . '</strong>';
                $row[] = $alert['nama_pasien'];

                // Badge kategori
                $badge_class = '';
                switch($alert['kategori']) {
                    case 'High': $badge_class = 'bg-danger'; break;
                    case 'Medium': $badge_class = 'bg-warning'; break;
                    case 'Low': $badge_class = 'bg-success'; break;
                }
                $row[] = '<span class="badge ' . $badge_class . '">' . $alert['kategori'] . '</span>';

                $row[] = substr($alert['keterangan'], 0, 50) . (strlen($alert['keterangan']) > 50 ? '...' : '');

                // Status berdasarkan WhatsApp logs
                $whatsapp_logs = $this->AlertPasien_model->get_whatsapp_logs_by_alert($alert['id']);
                $sent_count = 0;
                $failed_count = 0;

                foreach($whatsapp_logs as $log) {
                    // Handle both uppercase and lowercase field names
                    $status = isset($log['status']) ? $log['status'] : (isset($log['STATUS']) ? $log['STATUS'] : '');
                    if($status == 'sent') $sent_count++;
                    else $failed_count++;
                }

                if($sent_count > 0 && $failed_count == 0) {
                    $status = '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Terkirim (' . $sent_count . ')</span>';
                } elseif($sent_count > 0 && $failed_count > 0) {
                    $status = '<span class="badge bg-warning"><i class="fas fa-exclamation-triangle me-1"></i>Sebagian (' . $sent_count . '/' . ($sent_count + $failed_count) . ')</span>';
                } else {
                    $status = '<span class="badge bg-danger"><i class="fas fa-times me-1"></i>Gagal</span>';
                }
                $row[] = $status;

                $data[] = $row;
            }

            $output = array(
                "draw" => intval($draw),
                "recordsTotal" => $total_riwayat,
                "recordsFiltered" => $filtered_riwayat,
                "data" => $data
            );

            echo json_encode($output);
        } catch (Exception $e) {
            // Return error response for DataTables
            $output = array(
                "draw" => intval($draw),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => "Error loading data: " . $e->getMessage()
            );
            echo json_encode($output);
        }
    }

    /**
     * Format pesan WhatsApp
     */
    private function format_whatsapp_message($norm, $nama_pasien, $kategori, $keterangan) {
        // Tentukan salam berdasarkan waktu
        $jam = date("H");
        if ($jam >= '05' && $jam < "10") {
            $selamat = "Pagi";
        } elseif ($jam >= '10' && $jam < "15") {
            $selamat = "Siang";
        } elseif ($jam >= '15' && $jam < "19") {
            $selamat = "Sore";
        } else {
            $selamat = "Malam";
        }

        // Tentukan pembukaan berdasarkan prioritas
        $pembukaan = '';
        switch($kategori) {
            case 'High':
                $pembukaan = 'kami sampaikan Alert Pasien dengan *PRIORITAS TINGGI*';
                break;
            case 'Medium':
                $pembukaan = 'kami sampaikan Alert Pasien dengan *PRIORITAS SEDANG*';
                break;
            case 'Low':
                $pembukaan = 'kami sampaikan Alert Pasien dengan *PRIORITAS RENDAH*';
                break;
        }

        // Format pesan lengkap tanpa spasi berlebihan atau karakter khusus
        $isi_pesan = "Pasien dengan nama *$nama_pasien* (No. MR: *$norm*) memerlukan perhatian. ";
        $isi_pesan .= "*Keterangan:* $keterangan. ";
        $isi_pesan .= "Mohon untuk segera menindaklanjuti sesuai dengan prioritas yang diberikan.";

        // Format pesan sesuai template WhatsApp RS Dharmais
        $pesan = array(
            $selamat,      // Parameter 1: Salam (Pagi/Siang/Sore/Malam)
            $pembukaan,    // Parameter 2: Pembukaan/konteks
            $isi_pesan     // Parameter 3: Isi pesan lengkap
        );

        return $pesan;
    }

    /**
     * Halaman riwayat alert
     */
    public function riwayat() {
        $data['title'] = 'Riwayat Alert Pasien';
        $data['page_content'] = 'alert_pasien_riwayat';
        
        // Pagination
        $limit = 20;
        $offset = ($this->input->get('page') ?: 1 - 1) * $limit;
        
        $data['alert_history'] = $this->AlertPasien_model->get_alert_history($limit, $offset);
        
        $this->load->view('template_dashboard', $data);
    }

    /**
     * Detail alert
     */
    public function detail($alert_id) {
        $data['title'] = 'Detail Alert Pasien';
        $data['page_content'] = 'alert_pasien_detail';
        
        $data['alert_detail'] = $this->AlertPasien_model->get_alert_detail($alert_id);
        
        if(!$data['alert_detail']) {
            show_404();
        }
        
        $this->load->view('template_dashboard', $data);
    }

    /**
     * Test connection
     */
    public function test_connection() {
        echo "AlertPasien Controller is working! Current time: " . date('Y-m-d H:i:s');
    }
}
