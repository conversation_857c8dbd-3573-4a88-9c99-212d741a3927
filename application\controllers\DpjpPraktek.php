
<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class DpjpPraktek extends CI_Controller {

    function __construct()
    {
        parent::__construct();
        date_default_timezone_set("Asia/Bangkok");
        $this->load->helper('url');
        $this->load->model('Model_pulang');
        $this->load->library('session');
        
        // Cek session login
        if($this->session->userdata('status_login') != 'oke'){
            redirect('Auth/login');
        }
    }

    public function index() {
        $this->load->view('v_dpjp_praktek');
    }

    public function test_connection() {
        echo "Controller is working! Current time: " . date('Y-m-d H:i:s');
    }

    public function get_dpjp_praktek_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');
            $filter_kedatangan = $this->input->post('filter_kedatangan') ?? 'semua';

            // Debug: Log the parameters
            log_message('debug', 'DPJP Praktek - tglAwal: ' . $tglAwal . ', tglAkhir: ' . $tglAkhir . ', filter: ' . $filter_kedatangan);

            // Choose appropriate function based on filter
            switch($filter_kedatangan) {
                case 'tepat_waktu':
                    $listData = $this->Model_pulang->lisdpjppraktek_tepat_waktu($tglAwal, $tglAkhir);
                    break;
                case 'terlambat':
                    $listData = $this->Model_pulang->lisdpjppraktek_terlambat($tglAwal, $tglAkhir);
                    break;
                case 'lebih_awal':
                    $listData = $this->Model_pulang->lisdpjppraktek_lebih_awal($tglAwal, $tglAkhir);
                    break;
                case 'tidak_ada_data':
                    $listData = $this->Model_pulang->lisdpjppraktek_tidak_ada_data($tglAwal, $tglAkhir);
                    break;
                default:
                    $listData = $this->Model_pulang->lisdpjppraktek($tglAwal, $tglAkhir);
                    break;
            }

            if (!$listData) {
                throw new Exception('Query failed or returned null');
            }

            $data = array();
            $totalRows = $listData->num_rows();

            // Debug: Log the number of rows
            log_message('debug', 'DPJP Praktek - Total rows: ' . $totalRows . ' for filter: ' . $filter_kedatangan);

            if ($totalRows > 0) {
                foreach ($listData->result() as $row) {
                    $data[] = array(
                        $row->NORM ?? '',
                        $row->PASIEN ?? '',
                        $row->STATUSPENGUNJUNG ?? '',
                        $row->DOKTER ?? '',
                        $row->RUANGAN_PRAKTEK ?? '',
                        $row->RUANGAN_PERJANJIAN ?? '',
                        $row->TANGGAL_PENDAFTARAN ?? '',
                        $row->TANGGAL_RENCANA_PERJANJIAN ?? '',
                        $row->JADWAL_MULAI_PRAKTEK ?? '',
                        $row->JAM_BOARDING_2 ?? '',
                        $row->JAM_WAKTU_MULAI ?? '',
                        $row->JAM_CPPT ?? '',
                        $row->DURASI_MULAI_VS_JADWAL ?? '',
                        $row->KEDATANGAN ?? ''
                    );
                }
            }

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => $totalRows,
                "recordsFiltered" => $totalRows,
                "data" => $data
            );

            echo json_encode($output);

        } catch (Exception $e) {
            log_message('error', 'DPJP Praktek Error: ' . $e->getMessage());

            $output = array(
                "draw" => intval($this->input->post("draw")),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => $e->getMessage()
            );

            echo json_encode($output);
        }
    }

    public function get_summary_data() {
        try {
            $tglAwal = $this->input->post('tgl_awal') ?? date('Y-m-d 00:00:00');
            $tglAkhir = $this->input->post('tgl_akhir') ?? date('Y-m-d 23:59:59');

            // Get data from each specific function
            $tepatWaktuData = $this->Model_pulang->lisdpjppraktek_tepat_waktu($tglAwal, $tglAkhir);
            $terlambatData = $this->Model_pulang->lisdpjppraktek_terlambat($tglAwal, $tglAkhir);
            $lebihAwalData = $this->Model_pulang->lisdpjppraktek_lebih_awal($tglAwal, $tglAkhir);
            $tidakAdaDataData = $this->Model_pulang->lisdpjppraktek_tidak_ada_data($tglAwal, $tglAkhir);
            $allData = $this->Model_pulang->lisdpjppraktek($tglAwal, $tglAkhir);

            $summary = array(
                'total_pasien' => $allData ? $allData->num_rows() : 0,
                'tepat_waktu' => $tepatWaktuData ? $tepatWaktuData->num_rows() : 0,
                'terlambat' => $terlambatData ? $terlambatData->num_rows() : 0,
                'lebih_awal' => $lebihAwalData ? $lebihAwalData->num_rows() : 0,
                'tidak_ada_data' => $tidakAdaDataData ? $tidakAdaDataData->num_rows() : 0
            );

            echo json_encode($summary);

        } catch (Exception $e) {
            log_message('error', 'DPJP Summary Error: ' . $e->getMessage());

            $summary = array(
                'total_pasien' => 0,
                'tepat_waktu' => 0,
                'terlambat' => 0,
                'lebih_awal' => 0,
                'tidak_ada_data' => 0,
                'error' => $e->getMessage()
            );

            echo json_encode($summary);
        }
    }

    public function test_query() {
        $tglAwal = date('Y-m-d 00:00:00');
        $tglAkhir = date('Y-m-d 23:59:59');

        echo "Testing query with dates: $tglAwal to $tglAkhir<br>";

        try {
            $listData = $this->Model_pulang->lisdpjppraktek($tglAwal, $tglAkhir);

            if ($listData) {
                $totalRows = $listData->num_rows();
                echo "Query executed successfully. Total rows: $totalRows<br>";

                if ($totalRows > 0) {
                    echo "<h3>Sample Data (First 3 rows):</h3>";
                    $count = 0;
                    foreach ($listData->result() as $row) {
                        if ($count >= 3) break;
                        echo "<pre>";
                        print_r($row);
                        echo "</pre><hr>";
                        $count++;
                    }
                } else {
                    echo "No data found for the specified date range.";
                }
            } else {
                echo "Query returned null or false.";
            }
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage();
        }
    }

    public function export_excel() {
        // Excel export functionality - to be implemented
        echo "Excel export functionality - coming soon";
    }
}




