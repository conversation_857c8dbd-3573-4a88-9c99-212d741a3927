


<!-- Page Title Section -->
<div class="container-fluid py-3">
    <div class="row">
        <div class="col-md-6">
            <h3 class="mb-0">MONITORING HASIL LAB PA GEDUNG CENDANA</h3>
        </div>
        <div class="col-md-3 text-center">
            <div id="tanggalku" style="font-size: 24px; color: #2c3e50; font-weight: bold;"></div>
        </div>
        <div class="col-md-3 text-end">
            <h5 class="mb-1"><?= date("l, d F Y") ?></h5>
            <h6 class="total_pasien text-muted"><b>Total Pemeriksaan ()</b></h6>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container-fluid pb-4">
    <div class="row">
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-body">
                    <form id="filterForm" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tanggal Awal</label>
                                <input type="datetime-local" class="form-control" id="tgl_awal" name="tgl_awal" value="<?= date('Y-m-d 00:00:00') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Tanggal Akhir</label>
                                <input type="datetime-local" class="form-control" id="tgl_akhir" name="tgl_akhir" value="<?= date('Y-m-d 23:59:59') ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" id="btnFilter" class="btn btn-primary form-control">Filter</button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Filter Status</label>
                                <select class="form-control" id="status_filter" name="status_filter">
                                    <option value="">Semua Status</option>
                                    <option value="hijau" style="background-color: #10ac84; color: white;">🟢 Hijau (Final + Tepat Waktu)</option>
                                    <option value="kuning" style="background-color: #feb019d9; color: white;">🟡 Kuning (Final + Perhatian)</option>
                                    <option value="merah" style="background-color: #c9001dd9; color: white;">🔴 Merah (Final + Terlambat)</option>
                                    <option value="abu_abu" style="background-color: #33b2ff; color: white;">🔵 Biru (Belum Final + Tepat Waktu)</option>
                                    <option value="cream" style="background-color: #9f9ca5; color: white;">⚫ Abu-abu (Belum Final + Perhatian)</option>
                                    <option value="hitam" style="background-color: #000000; color: white;">⚫ Hitam (Belum Final + Terlambat)</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="myTable" class="display" style="width:100%">
                            <thead>
                                <tr style="text-align:center">
                                    <th>No. Lab</th>
                                    <th>Nama Pasien</th>
                                    <th>No. RM</th>
                                    <th>DOKTER</th>
                                    <th>Ruang Asal</th>
                                    <th>Masuk Sampel</th>
                                    <th>Tanggal Hasil</th>
                                    <th style="text-align: center;">Lama Pemeriksaan</th>
                                    <th>Jenis</th>
                                    <th>Status</th>
                                    <th>JENIS PEMERIKSAAN</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Wait for template scripts to load
function initMonitoringTable() {
    // Check if jQuery and DataTables are available
    if (typeof jQuery === 'undefined') {
        console.log('jQuery not loaded yet, retrying...');
        setTimeout(initMonitoringTable, 100);
        return;
    }

    if (typeof jQuery.fn.DataTable === 'undefined') {
        console.log('DataTables not loaded yet, retrying...');
        setTimeout(initMonitoringTable, 100);
        return;
    }

    $(document).ready(function() {
    var table = $('#myTable').DataTable({
        responsive: true,
        processing: true,
        serverSide: false,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
                text: '<i class="fas fa-file-pdf"></i> Export PDF',
                className: 'btn btn-danger btn-sm',
                title: 'Monitoring PA Cendana - ' + new Date().toLocaleDateString('id-ID'),
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10] // All visible columns
                },
                customize: function(doc) {
                    doc.defaultStyle.fontSize = 8;
                    doc.styles.tableHeader.fontSize = 9;
                    doc.styles.tableHeader.fillColor = '#2c3e50';
                    doc.content[1].table.widths = ['8%', '15%', '8%', '12%', '10%', '12%', '12%', '8%', '8%', '7%', '10%'];
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fas fa-file-excel"></i> Export Excel',
                className: 'btn btn-success btn-sm',
                title: 'Monitoring PA Cendana - ' + new Date().toLocaleDateString('id-ID'),
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10] // All visible columns
                }
            }
        ],
        ajax: {
            url: 'MonitoringPaCendana/get_monitoring_data',
            type: 'POST',
            data: function(d) {
                d.tgl_awal = $('#tgl_awal').val();
                d.tgl_akhir = $('#tgl_akhir').val();
                d.status_filter = $('#status_filter').val();
            },
            dataSrc: function(json) {
                if (json.recordsTotal !== undefined) {
                    $(".total_pasien").text('Total Pemeriksaan (' + json.recordsTotal + ')');
                }
                return json.data || [];
            }
        },
        columnDefs: [
            {
                targets: [0, 2, 7, 8, 9], // Center align certain columns
                className: 'text-center'
            },
            {
                targets: [9], // Status column
                render: function(data, type, row) {
                    return '<span class="' + data + '">' + data.toUpperCase() + '</span>';
                }
            }
        ],
        rowCallback: function(row, data, index) {
            // Apply row coloring based on status (data[9] is the status column)
            var status = data[9];
            if (status) {
                $(row).addClass(status);
            }
        },
        order: [[0, 'desc']], // Order by No. Lab descending
        pageLength: 100,
        lengthMenu: [25, 50, 100, 200]
    });

    $('#btnFilter').on('click', function() {
        table.ajax.reload();
    });

    $('#status_filter').on('change', function() {
        table.ajax.reload();
    });

    function reloadDatatable() {
        $('#myTable').DataTable().ajax.reload(null, false);
    }
    
    setInterval(reloadDatatable, 300000); // Refresh every 5 minutes

    // Clock function - only initialize if element exists and no other clock is running
    if (document.getElementById("tanggalku") && !window.clockInitialized) {
        window.clockInitialized = true;

        function updateClock() {
            var tanggal = new Date();
            var clockElement = document.getElementById("tanggalku");
            if (clockElement) {
                var hours = tanggal.getHours().toString().padStart(2, '0');
                var minutes = tanggal.getMinutes().toString().padStart(2, '0');
                var seconds = tanggal.getSeconds().toString().padStart(2, '0');
                clockElement.innerHTML = hours + " : " + minutes + " : " + seconds;
            }
        }

        // Update immediately and then every second
        updateClock();
        setInterval(updateClock, 1000);
    }

    }); // End document ready
}

// Initialize the monitoring table
initMonitoringTable();
</script>

